<?php

/**
 * Access Control Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Access Control Class
 */
class VideoBookingAccessControl
{

    /**
     * Initialize access control
     */
    public static function init()
    {
        // Hook into WordPress authentication
        add_action('wp_login', array(__CLASS__, 'on_user_login'), 10, 2);
        add_action('wp_logout', array(__CLASS__, 'on_user_logout'));

        // Add access control checks
        add_action('template_redirect', array(__CLASS__, 'check_page_access'));

        // Clean up expired access
        add_action('video_booking_cleanup_access', array(__CLASS__, 'cleanup_expired_access'));

        // Process scheduled video releases
        add_action('video_booking_process_releases', array(__CLASS__, 'process_scheduled_releases'));

        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('video_booking_cleanup_access')) {
            wp_schedule_event(time(), 'daily', 'video_booking_cleanup_access');
        }

        // Schedule release processing if not already scheduled
        if (!wp_next_scheduled('video_booking_process_releases')) {
            wp_schedule_event(time(), 'hourly', 'video_booking_process_releases');
        }
    }

    /**
     * Check if user has valid access to video
     */
    public static function verify_video_access($user_id, $video_id)
    {
        if (!$user_id || !$video_id) {
            return array(
                'has_access' => false,
                'reason' => 'Invalid user or video ID'
            );
        }

        // Get user access record
        $access = VideoBookingDB::get_user_access($user_id, $video_id);

        if (!$access) {
            return array(
                'has_access' => false,
                'reason' => 'No purchase record found'
            );
        }

        // Check if access has been granted
        if (!$access->access_granted_at) {
            return array(
                'has_access' => false,
                'reason' => 'Access not yet granted (early bird video pending)',
                'is_early_bird' => true
            );
        }

        // Check if access has expired
        if ($access->expires_at && strtotime($access->expires_at) < current_time('timestamp')) {
            return array(
                'has_access' => false,
                'reason' => 'Access has expired',
                'expired_at' => $access->expires_at
            );
        }

        return array(
            'has_access' => true,
            'access_record' => $access
        );
    }

    /**
     * Grant access to early bird videos when they become available
     */
    public static function grant_early_bird_access($video_id)
    {
        global $wpdb;

        $video = VideoBookingDB::get_video($video_id);
        if (!$video || !$video->is_early_bird) {
            return false;
        }

        // Update video status to uploaded
        VideoBookingDB::update_video($video_id, array(
            'upload_status' => 'uploaded'
        ));

        // Get all users with early bird access to this video
        $access_table = $wpdb->prefix . 'video_booking_access';
        $early_bird_users = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $access_table WHERE video_id = %d AND is_early_bird = 1 AND (access_granted_at IS NULL OR access_granted_at = '0000-00-00 00:00:00')",
            $video_id
        ));

        $granted_count = 0;
        $current_time = current_time('mysql');

        foreach ($early_bird_users as $access) {
            // Calculate expiry date from now
            $expires_at = date('Y-m-d H:i:s', strtotime("+{$video->duration_days} days"));

            // Grant access
            $result = $wpdb->update(
                $access_table,
                array(
                    'access_granted_at' => $current_time,
                    'expires_at' => $expires_at
                ),
                array('id' => $access->id)
            );

            if ($result) {
                $granted_count++;

                // Send notification email
                // VideoBookingEmailFunctions::send_early_bird_notification($access->user_id, $video_id);
            }
        }

        return $granted_count;
    }

    /**
     * Process scheduled video releases
     */
    public static function process_scheduled_releases()
    {
        global $wpdb;

        $videos_table = $wpdb->prefix . 'video_booking_videos';
        $current_time = current_time('mysql');

        // Find videos that are scheduled for release and the time has come
        $scheduled_videos = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $videos_table
             WHERE is_early_bird = 1
             AND upload_status = 'pending'
             AND release_date IS NOT NULL
             AND release_date <= %s",
            $current_time
        ));

        $processed_count = 0;

        foreach ($scheduled_videos as $video) {
            // Update video status to uploaded
            $result = VideoBookingDB::update_video($video->id, array(
                'upload_status' => 'uploaded'
            ));

            if ($result !== false) {
                // Grant access to early bird customers
                $granted_count = self::grant_early_bird_access($video->id);

                if ($granted_count !== false) {
                    $processed_count++;
                    error_log("Video booking: Released video '{$video->title}' (ID: {$video->id}) to {$granted_count} early bird customers");
                }
            }
        }

        if ($processed_count > 0) {
            error_log("Video booking: Processed {$processed_count} scheduled video releases");
        }

        return $processed_count;
    }



    /**
     * Check page access permissions
     */
    public static function check_page_access()
    {
        $video_page = get_query_var('video_page');

        // Protect video streaming pages
        if ($video_page === 'stream') {
            if (!is_user_logged_in()) {
                $current_url = home_url($_SERVER['REQUEST_URI']);
                $login_url = method_exists('VideoBookingAuth', 'get_login_url') ? VideoBookingAuth::get_login_url($current_url) : home_url('/login?redirect_to=' . urlencode($current_url));
                wp_redirect($login_url);
                exit;
            }

            $video_id = get_query_var('video_id');
            if ($video_id) {
                $access_check = self::verify_video_access(get_current_user_id(), $video_id);
                if (!$access_check['has_access']) {
                    wp_redirect(home_url('/my-account/my-recordings?error=' . urlencode($access_check['reason'])));
                    exit;
                }
            }
        }

        // Protect my recordings page
        if ($video_page === 'my_recordings') {
            if (!is_user_logged_in()) {
                $redirect = home_url('/my-account/my-recordings');
                $login_url = method_exists('VideoBookingAuth', 'get_login_url') ? VideoBookingAuth::get_login_url($redirect) : home_url('/video-user-login?redirect_to=' . urlencode($redirect));
                wp_redirect($login_url);
                exit;
            }
        }
    }

    /**
     * Clean up expired access records
     */
    public static function cleanup_expired_access()
    {
        global $wpdb;

        $access_table = $wpdb->prefix . 'video_booking_access';
        $current_time = current_time('mysql');

        // Get expired access records
        $expired_records = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $access_table WHERE expires_at IS NOT NULL AND expires_at < %s",
            $current_time
        ));

        $cleanup_count = 0;

        foreach ($expired_records as $record) {
            // Log the expiry
            error_log("Video access expired for user {$record->user_id}, video {$record->video_id}");

            // Optionally send expiry notification
            self::send_access_expiry_notification($record->user_id, $record->video_id);

            $cleanup_count++;
        }

        if ($cleanup_count > 0) {
            error_log("Cleaned up {$cleanup_count} expired video access records");
        }

        return $cleanup_count;
    }

    /**
     * Send access expiry notification
     */
    private static function send_access_expiry_notification($user_id, $video_id)
    {
        $user = get_user_by('id', $user_id);
        $video = VideoBookingDB::get_video($video_id);

        if (!$user || !$video) {
            return false;
        }

        $subject = 'Video Access Expired - Repurchase Available';
        $video_url = home_url('/workshop-recordings');

        $message = "
        <h2>Your video access has expired</h2>
        
        <p>Hi {$user->display_name},</p>
        
        <p>Your access to the following workshop recording has expired:</p>
        
        <h3>{$video->title}</h3>
        
        <p>If you'd like to continue watching, you can repurchase the video:</p>
        <p><a href='{$video_url}' style='background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Browse Videos</a></p>
        
        <p>Thank you for being part of our learning community!</p>
        
        <p>Best regards,<br>Cosmic Tree Foundation Team</p>
        ";

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        return wp_mail($user->user_email, $subject, $message, $headers);
    }

    /**
     * Handle user login
     */
    public static function on_user_login($user_login, $user)
    {
        // Log user login for security
        error_log("Video booking user login: {$user->user_email} (ID: {$user->ID})");

        // Update last login time
        update_user_meta($user->ID, 'video_booking_last_login', current_time('mysql'));
    }

    /**
     * Handle user logout
     */
    public static function on_user_logout()
    {
        $user_id = get_current_user_id();
        if ($user_id) {
            error_log("Video booking user logout: User ID {$user_id}");
            update_user_meta($user_id, 'video_booking_last_logout', current_time('mysql'));
        }
    }

    /**
     * Get user's video access summary
     */
    public static function get_user_access_summary($user_id)
    {
        $access_records = VideoBookingDB::get_user_access($user_id);

        $summary = array(
            'total_videos' => 0,
            'accessible_videos' => 0,
            'expired_videos' => 0,
            'pending_videos' => 0,
            'expiring_soon' => 0
        );

        foreach ($access_records as $access) {
            $summary['total_videos']++;

            if (!$access->access_granted_at) {
                $summary['pending_videos']++;
            } elseif ($access->expires_at && strtotime($access->expires_at) < current_time('timestamp')) {
                $summary['expired_videos']++;
            } else {
                $summary['accessible_videos']++;

                // Check if expiring within 7 days
                if ($access->expires_at && strtotime($access->expires_at) < strtotime('+7 days')) {
                    $summary['expiring_soon']++;
                }
            }
        }

        return $summary;
    }

    /**
     * Validate session security
     */
    public static function validate_session_security()
    {
        if (!is_user_logged_in()) {
            return false;
        }

        $user_id = get_current_user_id();
        $current_ip = self::get_client_ip();
        $stored_ip = get_user_meta($user_id, 'video_booking_session_ip', true);

        // Store IP on first access
        if (!$stored_ip) {
            update_user_meta($user_id, 'video_booking_session_ip', $current_ip);
            return true;
        }

        // Check for IP changes (basic security check)
        if ($stored_ip !== $current_ip) {
            error_log("IP change detected for user {$user_id}: {$stored_ip} -> {$current_ip}");
            // You might want to implement additional security measures here
        }

        return true;
    }

    /**
     * Get client IP address
     */
    private static function get_client_ip()
    {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

// Initialize access control
VideoBookingAccessControl::init();

// AJAX handler for scheduling video release
add_action('wp_ajax_schedule_video_release', 'schedule_video_release_ajax');

function schedule_video_release_ajax()
{
    check_ajax_referer('video_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    $video_id = absint($_POST['video_id']);
    $release_date = sanitize_text_field($_POST['release_date']);
    $immediate_release = !empty($_POST['immediate_release']);

    if (!$video_id) {
        wp_send_json_error('Invalid video ID');
    }

    $video = VideoBookingDB::get_video($video_id);
    if (!$video || !$video->is_early_bird) {
        wp_send_json_error('Video not found or not an early bird video');
    }

    // Prepare update data
    $update_data = array();

    if ($immediate_release) {
        // Release immediately
        $update_data['upload_status'] = 'uploaded';
        $update_data['release_date'] = current_time('mysql');

        // Grant access immediately
        $granted_count = VideoBookingAccessControl::grant_early_bird_access($video_id);
        $message = "Video released immediately. Access granted to {$granted_count} early bird customers.";
    } else {
        // Schedule for future release
        if (empty($release_date)) {
            wp_send_json_error('Release date is required');
        }

        // Validate date is in the future
        if (strtotime($release_date) <= current_time('timestamp')) {
            wp_send_json_error('Release date must be in the future');
        }

        $update_data['upload_status'] = 'pending';
        $update_data['release_date'] = $release_date;
        $message = "Video scheduled for release on " . date('M j, Y g:i A', strtotime($release_date));
    }

    // Update video
    $result = VideoBookingDB::update_video($video_id, $update_data);

    if ($result !== false) {
        wp_send_json_success(array('message' => $message));
    } else {
        wp_send_json_error('Failed to schedule video release');
    }
}
