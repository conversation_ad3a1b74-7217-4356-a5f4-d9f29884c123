<?php

/**
 * Secure Video Streaming Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Streaming Class
 */
class VideoBookingStream
{

    /**
     * Stream video with access control
     */
    public static function stream_video($video_id, $user_id = null)
    {
        // Get current user if not provided
        if (!$user_id) {
            if (!is_user_logged_in()) {
                self::access_denied('Please log in to watch videos');
                return;
            }
            $user_id = get_current_user_id();
        }

        // Get video details
        $video = VideoBookingDB::get_video($video_id);
        if (!$video) {
            self::access_denied('Video not found');
            return;
        }

        // Check if user has access
        if (!VideoBookingDB::has_video_access($user_id, $video_id)) {
            self::access_denied('You do not have access to this video');
            return;
        }

        // Check if video file exists based on source
        $video_source = $video->video_source ?? 'private';

        if ($video_source === 'media') {
            // For media library videos, check if URL is accessible
            if (filter_var($video->video_file, FILTER_VALIDATE_URL)) {
                // It's a URL, redirect to it directly for media library videos
                wp_redirect($video->video_file);
                exit;
            } else {
                // It's a relative path, construct full path
                $video_path = ABSPATH . ltrim($video->video_file, '/');
                if (!file_exists($video_path)) {
                    self::access_denied('Video file not found');
                    return;
                }
            }
        } else {
            // Private storage
            $video_path = VIDEO_PRIVATE_PATH . $video->video_file;
            if (!file_exists($video_path)) {
                self::access_denied('Video file not found');
                return;
            }
        }

        // Log access attempt
        self::log_video_access($user_id, $video_id);

        // Serve the video file based on source
        if ($video_source === 'media' && filter_var($video->video_file, FILTER_VALIDATE_URL)) {
            // For media library URLs, we already redirected above
            return;
        } else {
            // Serve the video file with range support
            self::serve_video_file($video_path, $video->title);
        }
    }

    /**
     * Serve video file with range support
     */
    private static function serve_video_file($file_path, $title = '')
    {
        $file_size = filesize($file_path);
        $file_name = basename($file_path);

        // Set headers
        header('Content-Type: video/mp4');
        header('Content-Disposition: inline; filename="' . $file_name . '"');
        header('Accept-Ranges: bytes');
        header('Content-Length: ' . $file_size);

        // Security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('Cache-Control: private, no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Handle range requests for video seeking
        if (isset($_SERVER['HTTP_RANGE'])) {
            self::serve_range_request($file_path, $file_size);
        } else {
            // Serve entire file
            readfile($file_path);
        }

        exit;
    }

    /**
     * Handle HTTP range requests for video seeking
     */
    private static function serve_range_request($file_path, $file_size)
    {
        $range = $_SERVER['HTTP_RANGE'];

        // Parse range header
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = !empty($matches[2]) ? intval($matches[2]) : $file_size - 1;

            // Validate range
            if ($start > $end || $start >= $file_size || $end >= $file_size) {
                header('HTTP/1.1 416 Requested Range Not Satisfiable');
                header('Content-Range: bytes */' . $file_size);
                exit;
            }

            $content_length = $end - $start + 1;

            // Set partial content headers
            header('HTTP/1.1 206 Partial Content');
            header('Content-Range: bytes ' . $start . '-' . $end . '/' . $file_size);
            header('Content-Length: ' . $content_length);

            // Stream the requested range
            $file = fopen($file_path, 'rb');
            fseek($file, $start);

            $buffer_size = 8192;
            $bytes_remaining = $content_length;

            while ($bytes_remaining > 0 && !feof($file)) {
                $bytes_to_read = min($buffer_size, $bytes_remaining);
                echo fread($file, $bytes_to_read);
                $bytes_remaining -= $bytes_to_read;

                // Flush output to prevent memory issues
                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }

            fclose($file);
        }
    }

    /**
     * Generate secure streaming URL
     */
    public static function get_streaming_url($video_id, $user_id = null)
    {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Get video details to check source
        $video = VideoBookingDB::get_video($video_id);
        if (!$video) {
            return '';
        }

        $video_source = $video->video_source ?? 'private';

        if ($video_source === 'media' && filter_var($video->video_file, FILTER_VALIDATE_URL)) {
            // For media library URLs, return the direct URL (still need access control)
            $token = self::generate_streaming_token($video_id, $user_id);
            return home_url('/video-stream/' . $video_id . '?token=' . $token);
        } else {
            // Generate secure token for private videos
            $token = self::generate_streaming_token($video_id, $user_id);
            return home_url('/video-stream/' . $video_id . '?token=' . $token);
        }
    }

    /**
     * Generate secure streaming token
     */
    private static function generate_streaming_token($video_id, $user_id)
    {
        $timestamp = time();
        $expiry = $timestamp + (60 * 60); // Token expires in 1 hour

        $data = $video_id . '|' . $user_id . '|' . $expiry;
        $hash = hash_hmac('sha256', $data, wp_salt('secure_auth'));

        return base64_encode($data . '|' . $hash);
    }

    /**
     * Verify streaming token
     */
    public static function verify_streaming_token($token, $video_id, $user_id)
    {
        $decoded = base64_decode($token);
        $parts = explode('|', $decoded);

        if (count($parts) !== 4) {
            return false;
        }

        list($token_video_id, $token_user_id, $expiry, $hash) = $parts;

        // Check if token matches request
        if ($token_video_id != $video_id || $token_user_id != $user_id) {
            return false;
        }

        // Check if token has expired
        if (time() > $expiry) {
            return false;
        }

        // Verify hash
        $data = $token_video_id . '|' . $token_user_id . '|' . $expiry;
        $expected_hash = hash_hmac('sha256', $data, wp_salt('secure_auth'));

        return hash_equals($expected_hash, $hash);
    }

    /**
     * Log video access for analytics
     */
    private static function log_video_access($user_id, $video_id)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_access_log';

        // Create log table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS $table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            video_id bigint(20) unsigned NOT NULL,
            access_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            ip_address varchar(45),
            user_agent text,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY video_id (video_id),
            KEY access_time (access_time)
        ) {$wpdb->get_charset_collate()};";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Log the access
        $wpdb->insert($table, array(
            'user_id' => $user_id,
            'video_id' => $video_id,
            'ip_address' => self::get_client_ip(),
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'], 0, 500)
        ));
    }

    /**
     * Get client IP address
     */
    private static function get_client_ip()
    {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Show access denied message
     */
    private static function access_denied($message = 'Access denied')
    {
        status_header(403);

        // Return JSON for AJAX requests
        if (wp_doing_ajax() || (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false)) {
            wp_send_json_error($message);
        }

        // Show HTML error page
?>
        <!DOCTYPE html>
        <html>

        <head>
            <title>Access Denied</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background: #f5f5f5;
                }

                .error-container {
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    display: inline-block;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                }

                .error-icon {
                    font-size: 4em;
                    color: #dc3545;
                    margin-bottom: 20px;
                }

                h1 {
                    color: #333;
                    margin-bottom: 20px;
                }

                p {
                    color: #666;
                    margin-bottom: 30px;
                }

                .btn {
                    background: #2c5aa0;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    display: inline-block;
                }

                .btn:hover {
                    background: #1e3f73;
                }
            </style>
        </head>

        <body>
            <div class="error-container">
                <div class="error-icon">🔒</div>
                <h1>Access Denied</h1>
                <p><?php echo esc_html($message); ?></p>
                <a href="<?php echo home_url('/my-account/my-recordings'); ?>" class="btn">Go to My Recordings</a>
            </div>
        </body>

        </html>
<?php
        exit;
    }

    /**
     * Check video access via AJAX
     */
    public static function check_video_access_ajax()
    {
        check_ajax_referer('video_booking_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error('Please log in to access videos');
        }

        $video_id = absint($_POST['video_id']);
        $user_id = get_current_user_id();

        $has_access = VideoBookingDB::has_video_access($user_id, $video_id);

        if ($has_access) {
            $streaming_url = self::get_streaming_url($video_id, $user_id);
            wp_send_json_success(array(
                'has_access' => true,
                'streaming_url' => $streaming_url
            ));
        } else {
            wp_send_json_error('You do not have access to this video');
        }
    }
}

// AJAX handler for video access check
add_action('wp_ajax_check_video_access', array('VideoBookingStream', 'check_video_access_ajax'));
add_action('wp_ajax_nopriv_check_video_access', array('VideoBookingStream', 'check_video_access_ajax'));

// Handle video streaming requests
add_action('template_redirect', function () {
    $video_page = get_query_var('video_page');
    $video_id = get_query_var('video_id');

    if ($video_page === 'stream' && $video_id) {
        // Verify token if provided
        if (isset($_GET['token'])) {
            $user_id = get_current_user_id();
            if (!VideoBookingStream::verify_streaming_token($_GET['token'], $video_id, $user_id)) {
                status_header(403);
                exit('Invalid or expired token');
            }
        }

        VideoBookingStream::stream_video($video_id);
    }
});
