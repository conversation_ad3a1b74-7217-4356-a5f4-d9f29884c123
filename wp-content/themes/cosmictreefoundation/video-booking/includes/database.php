<?php

/**
 * Database Helper Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Database Operations
 */
class VideoBookingDB
{

    /**
     * Get all videos with optional filters
     */
    public static function get_videos($args = array())
    {
        global $wpdb;

        $defaults = array(
            'status' => 'all', // all, available, early_bird
            'limit' => -1,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args($args, $defaults);

        $table = $wpdb->prefix . 'video_booking_videos';
        $where = array('1=1');

        if ($args['status'] === 'available') {
            $where[] = 'is_available = 1';
        } elseif ($args['status'] === 'early_bird') {
            $where[] = 'is_early_bird = 1';
        }

        $where_clause = implode(' AND ', $where);
        $order_clause = "ORDER BY {$args['orderby']} {$args['order']}";
        $limit_clause = $args['limit'] > 0 ? "LIMIT {$args['offset']}, {$args['limit']}" : '';

        $sql = "SELECT * FROM $table WHERE $where_clause $order_clause $limit_clause";

        return $wpdb->get_results($sql);
    }

    /**
     * Get single video by ID or slug
     */
    public static function get_video($identifier, $by = 'id')
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_videos';

        if ($by === 'id') {
            $sql = $wpdb->prepare("SELECT * FROM $table WHERE id = %d", $identifier);
        } else {
            $slug = sanitize_title($identifier);
            $sql = $wpdb->prepare("SELECT * FROM $table WHERE REPLACE(LOWER(title), ' ', '-') = %s", $slug);
        }

        return $wpdb->get_row($sql);
    }

    /**
     * Create new video
     */
    public static function create_video($data)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_videos';

        $defaults = array(
            'title' => '',
            'description' => '',
            'video_file' => '',
            'video_source' => 'private',
            'thumbnail' => '',
            'regular_price' => 0.00,
            'early_bird_price' => 0.00,
            'early_bird_note' => '',
            'early_bird_end_date' => null,
            'release_date' => null,
            'duration_days' => 30,
            'is_early_bird' => 0,
            'is_available' => 1,
            'upload_status' => 'completed'
        );

        $data = wp_parse_args($data, $defaults);

        $result = $wpdb->insert($table, $data, array(
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%f',
            '%f',
            '%s',
            '%s',
            '%s',
            '%d',
            '%d',
            '%d',
            '%s'
        ));

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Update video
     */
    public static function update_video($id, $data)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_videos';

        return $wpdb->update($table, $data, array('id' => $id));
    }

    /**
     * Delete video
     */
    public static function delete_video($id)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_videos';

        return $wpdb->delete($table, array('id' => $id), array('%d'));
    }

    /**
     * Get user's video access
     */
    public static function get_user_access($user_id, $video_id = null)
    {
        global $wpdb;

        $access_table = $wpdb->prefix . 'video_booking_access';
        $videos_table = $wpdb->prefix . 'video_booking_videos';

        $sql = "SELECT a.*, v.title, v.thumbnail, v.duration_days, v.release_date, v.is_early_bird
                FROM $access_table a
                JOIN $videos_table v ON a.video_id = v.id
                WHERE a.user_id = %d";

        $params = array($user_id);

        if ($video_id) {
            $sql .= " AND a.video_id = %d";
            $params[] = $video_id;
        }

        $sql .= " ORDER BY a.created_at DESC";

        if ($video_id) {
            return $wpdb->get_row($wpdb->prepare($sql, $params));
        } else {
            return $wpdb->get_results($wpdb->prepare($sql, $params));
        }
    }

    /**
     * Check if user has access to video
     */
    public static function has_video_access($user_id, $video_id)
    {
        $access = self::get_user_access($user_id, $video_id);

        if (!$access) {
            return false;
        }

        // Check if access is granted
        if (!$access->access_granted_at) {
            return false;
        }

        // Check if not expired
        if ($access->expires_at && strtotime($access->expires_at) < current_time('timestamp')) {
            return false;
        }

        return true;
    }

    /**
     * Grant video access to user
     */
    public static function grant_video_access($user_id, $video_id, $order_id, $is_early_bird = false)
    {
        global $wpdb;

        $access_table = $wpdb->prefix . 'video_booking_access';
        $video = self::get_video($video_id);

        if (!$video) {
            return false;
        }

        $access_granted_at = null;
        $expires_at = null;

        if ($is_early_bird) {
            // Early bird: access granted only when video is uploaded AND release date has passed
            $is_video_ready = ($video->upload_status === 'uploaded' || $video->upload_status === 'completed');
            $is_release_time = true; // Default to true if no release date set

            if ($video->release_date) {
                $is_release_time = strtotime($video->release_date) <= current_time('timestamp');
            }

            if ($is_video_ready && $is_release_time) {
                $access_granted_at = current_time('mysql');
                $expires_at = date('Y-m-d H:i:s', strtotime("+{$video->duration_days} days"));
            }
            // If not ready, both access_granted_at and expires_at remain NULL
        } else {
            // Regular: access granted immediately if video is completed
            if ($video->upload_status === 'completed') {
                $access_granted_at = current_time('mysql');
                $expires_at = date('Y-m-d H:i:s', strtotime("+{$video->duration_days} days"));
            }
        }

        $data = array(
            'user_id' => $user_id,
            'video_id' => $video_id,
            'order_id' => $order_id,
            'access_granted_at' => $access_granted_at,
            'expires_at' => $expires_at,
            'is_early_bird' => $is_early_bird ? 1 : 0
        );

        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $sql = "INSERT INTO $access_table (user_id, video_id, order_id, access_granted_at, expires_at, is_early_bird) 
                VALUES (%d, %d, %d, %s, %s, %d) 
                ON DUPLICATE KEY UPDATE 
                access_granted_at = VALUES(access_granted_at), 
                expires_at = VALUES(expires_at)";

        return $wpdb->query($wpdb->prepare(
            $sql,
            $user_id,
            $video_id,
            $order_id,
            $access_granted_at,
            $expires_at,
            $is_early_bird ? 1 : 0
        ));
    }

    /**
     * Create order
     */
    public static function create_order($data)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_orders';

        $defaults = array(
            'user_id' => 0,
            'order_number' => self::generate_order_number(),
            'total_amount' => 0.00,
            'coupon_code' => '',
            'coupon_discount' => 0.00,
            'payment_status' => 'pending',
            'customer_name' => '',
            'customer_email' => '',
            'customer_phone' => ''
        );

        $data = wp_parse_args($data, $defaults);

        $result = $wpdb->insert($table, $data);

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Generate unique order number
     */
    public static function generate_order_number()
    {
        return 'VB' . date('Ymd') . rand(1000, 9999);
    }

    /**
     * Add order item
     */
    public static function add_order_item($order_id, $video_id, $price, $is_early_bird = false)
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_order_items';

        $data = array(
            'order_id' => $order_id,
            'video_id' => $video_id,
            'price' => $price,
            'is_early_bird' => $is_early_bird ? 1 : 0
        );

        return $wpdb->insert($table, $data);
    }

    /**
     * Get order with items
     */
    public static function get_order($order_id, $with_items = true)
    {
        global $wpdb;

        $orders_table = $wpdb->prefix . 'video_booking_orders';
        $order = $wpdb->get_row($wpdb->prepare("SELECT * FROM $orders_table WHERE id = %d", $order_id));

        if ($order && $with_items) {
            $items_table = $wpdb->prefix . 'video_booking_order_items';
            $videos_table = $wpdb->prefix . 'video_booking_videos';

            $items_sql = "SELECT oi.*, v.title, v.thumbnail 
                         FROM $items_table oi 
                         JOIN $videos_table v ON oi.video_id = v.id 
                         WHERE oi.order_id = %d";

            $order->items = $wpdb->get_results($wpdb->prepare($items_sql, $order_id));
        }

        return $order;
    }

    /**
     * Update order status
     */
    public static function update_order_status($order_id, $status, $payment_data = array())
    {
        global $wpdb;

        $table = $wpdb->prefix . 'video_booking_orders';

        $data = array('payment_status' => $status);
        $data = array_merge($data, $payment_data);

        return $wpdb->update($table, $data, array('id' => $order_id));
    }
}
