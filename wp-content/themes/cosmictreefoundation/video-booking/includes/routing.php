<?php

/**
 * Video Booking URL Routing
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Booking Routing Class
 */
class VideoBookingRouting
{

    /**
     * Initialize routing
     */
    public static function init()
    {
        // Add rewrite rules
        add_action('init', array(__CLASS__, 'add_rewrite_rules'));

        // Add query vars
        add_filter('query_vars', array(__CLASS__, 'add_query_vars'));

        // Handle template loading
        add_action('template_redirect', array(__CLASS__, 'handle_template_loading'));

        // Enqueue scripts and styles for video pages
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_video_assets'));
    }

    /**
     * Add rewrite rules for video booking pages
     */
    public static function add_rewrite_rules()
    {
        // Workshop recordings listing page
        add_rewrite_rule(
            '^workshop-recordings/?$',
            'index.php?video_page=listing',
            'top'
        );

        // Individual video detail page
        add_rewrite_rule(
            '^workshop-recordings/([^/]+)/?$',
            'index.php?video_page=detail&video_slug=$matches[1]',
            'top'
        );

        // Video cart page
        add_rewrite_rule(
            '^video-cart/?$',
            'index.php?video_page=cart',
            'top'
        );

        // Video checkout page
        add_rewrite_rule(
            '^video-checkout/?$',
            'index.php?video_page=checkout',
            'top'
        );

        // My recordings page
        add_rewrite_rule(
            '^my-account/my-recordings/?$',
            'index.php?video_page=my_recordings',
            'top'
        );

        // Video streaming page
        add_rewrite_rule(
            '^video-stream/([0-9]+)/?$',
            'index.php?video_page=stream&video_id=$matches[1]',
            'top'
        );

        // Flush rewrite rules if needed
        if (get_option('video_booking_rewrite_flushed') !== VIDEO_BOOKING_VERSION) {
            flush_rewrite_rules();
            update_option('video_booking_rewrite_flushed', VIDEO_BOOKING_VERSION);
        }
    }

    /**
     * Add query variables
     */
    public static function add_query_vars($vars)
    {
        $vars[] = 'video_page';
        $vars[] = 'video_slug';
        $vars[] = 'video_id';
        return $vars;
    }

    /**
     * Handle template loading for video pages
     */
    public static function handle_template_loading()
    {
        $video_page = get_query_var('video_page');

        if (!$video_page) {
            return;
        }

        // Check if corresponding WordPress page exists
        $page_slugs = array(
            'listing' => 'workshop-recordings',
            'detail' => 'workshop-recordings', // Detail pages use same base
            'cart' => 'video-cart',
            'checkout' => 'video-checkout',
            'my_recordings' => 'my-recordings',
            'stream' => 'video-stream'
        );

        if (isset($page_slugs[$video_page])) {
            $page = get_page_by_path($page_slugs[$video_page]);
            if (!$page) {
                // Page doesn't exist in WordPress, show 404
                global $wp_query;
                $wp_query->set_404();
                status_header(404);
                get_template_part('404');
                exit;
            }
        }

        // Define template mappings
        $templates = array(
            'listing' => 'listing.php',
            'detail' => 'detail.php',
            'cart' => 'cart.php',
            'checkout' => 'checkout.php',
            'my_recordings' => 'my_recordings.php',
            'stream' => 'stream.php'
        );

        if (!isset($templates[$video_page])) {
            return;
        }

        // Special case: When serving the actual video bytes via tokenized URL,
        // skip loading the stream template so the streaming handler can run.
        if ($video_page === 'stream' && isset($_GET['token'])) {
            // Let the streaming hook in `includes/video-stream.php` handle the request
            return;
        }

        // Check login requirement for checkout
        if ($video_page === 'checkout' && !is_user_logged_in()) {
            $redirect_to = home_url('/video-checkout');
            $login_url = method_exists('VideoBookingAuth', 'get_login_url') ? VideoBookingAuth::get_login_url($redirect_to) : home_url('/video-user-login?redirect_to=' . urlencode($redirect_to));
            wp_redirect($login_url);
            exit;
        }

        $template_file = VIDEO_BOOKING_PATH . 'templates/' . $templates[$video_page];

        if (file_exists($template_file)) {
            // Set page title
            self::set_page_title($video_page);

            // Load template
            include $template_file;
            exit;
        } else {
            // Template not found, show 404
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            get_template_part('404');
            exit;
        }
    }

    /**
     * Set page title based on video page type
     */
    private static function set_page_title($video_page)
    {
        $titles = array(
            'listing' => 'Workshop Recordings',
            'detail' => 'Video Details',
            'cart' => 'Your Cart',
            'checkout' => 'Checkout',
            'my_recordings' => 'My Recordings',
            'stream' => 'Video Player'
        );

        if (isset($titles[$video_page])) {
            add_filter('wp_title', function ($title) use ($titles, $video_page) {
                return $titles[$video_page] . ' | ' . get_bloginfo('name');
            });

            add_filter('document_title_parts', function ($title_parts) use ($titles, $video_page) {
                $title_parts['title'] = $titles[$video_page];
                return $title_parts;
            });
        }

        // Special handling for video detail pages
        if ($video_page === 'detail') {
            $video_slug = get_query_var('video_slug');
            if ($video_slug) {
                $video = VideoBookingDB::get_video($video_slug, 'slug');
                if ($video) {
                    add_filter('wp_title', function ($title) use ($video) {
                        return $video->title . ' | ' . get_bloginfo('name');
                    });

                    add_filter('document_title_parts', function ($title_parts) use ($video) {
                        $title_parts['title'] = $video->title;
                        return $title_parts;
                    });
                }
            }
        }

        // Special handling for video streaming pages
        if ($video_page === 'stream') {
            $video_id = get_query_var('video_id');
            if ($video_id) {
                $video = VideoBookingDB::get_video($video_id);
                if ($video) {
                    add_filter('wp_title', function ($title) use ($video) {
                        return 'Watching: ' . $video->title . ' | ' . get_bloginfo('name');
                    });

                    add_filter('document_title_parts', function ($title_parts) use ($video) {
                        $title_parts['title'] = 'Watching: ' . $video->title;
                        return $title_parts;
                    });
                }
            }
        }
    }

    /**
     * Enqueue assets for video pages
     */
    public static function enqueue_video_assets()
    {
        $video_page = get_query_var('video_page');

        if (!$video_page) {
            return;
        }

        // Enqueue main video booking styles and scripts
        wp_enqueue_style(
            'video-booking-main',
            VIDEO_BOOKING_URL . 'assets/css/video-booking.css',
            array(),
            VIDEO_BOOKING_VERSION
        );

        wp_enqueue_script(
            'video-booking-main',
            VIDEO_BOOKING_URL . 'assets/js/video-booking.js',
            array('jquery'),
            VIDEO_BOOKING_VERSION,
            true
        );

        // Localize script with necessary data
        wp_localize_script('video-booking-main', 'videoBooking', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('video_booking_nonce'),
            'site_url' => home_url(),
            'user_id' => get_current_user_id(),
            'is_logged_in' => is_user_logged_in(),
            'razorpay_key' => defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '',
            'currency' => 'INR',
            'messages' => array(
                'login_required' => 'Please log in to access this feature',
                'cart_empty' => 'Your cart is empty',
                'processing' => 'Processing...',
                'error' => 'An error occurred. Please try again.',
                'success' => 'Success!'
            )
        ));

        // Enqueue Razorpay script for checkout page
        if ($video_page === 'checkout' && defined('RAZORPAY_KEY')) {
            wp_enqueue_script(
                'razorpay-checkout',
                'https://checkout.razorpay.com/v1/checkout.js',
                array(),
                null,
                true
            );
        }

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css',
            array(),
            '4.7.0'
        );
    }

    /**
     * Get video page URL
     */
    public static function get_video_page_url($page, $params = array())
    {
        $urls = array(
            'listing' => home_url('/workshop-recordings'),
            'cart' => home_url('/video-cart'),
            'checkout' => home_url('/video-checkout'),
            'my_recordings' => home_url('/my-account/my-recordings')
        );

        if (isset($urls[$page])) {
            $url = $urls[$page];

            if (!empty($params)) {
                $url = add_query_arg($params, $url);
            }

            return $url;
        }

        return home_url();
    }

    /**
     * Get video detail URL
     */
    public static function get_video_detail_url($video)
    {
        if (is_object($video)) {
            $slug = sanitize_title($video->title);
        } else {
            $slug = sanitize_title($video);
        }

        return home_url('/workshop-recordings/' . $slug);
    }

    /**
     * Get video streaming URL
     */
    public static function get_video_stream_url($video_id)
    {
        return home_url('/video-stream/' . $video_id);
    }

    /**
     * Check if current page is a video booking page
     */
    public static function is_video_page()
    {
        return !empty(get_query_var('video_page'));
    }

    /**
     * Get current video page type
     */
    public static function get_current_video_page()
    {
        return get_query_var('video_page');
    }

    /**
     * Add body classes for video pages
     */
    public static function add_body_classes($classes)
    {
        $video_page = get_query_var('video_page');

        if ($video_page) {
            $classes[] = 'video-booking-page';
            $classes[] = 'video-page-' . $video_page;
        }

        return $classes;
    }

    /**
     * Handle 404 errors for video pages
     */
    public static function handle_video_404()
    {
        $video_page = get_query_var('video_page');

        if ($video_page === 'detail') {
            $video_slug = get_query_var('video_slug');
            $video = VideoBookingDB::get_video($video_slug, 'slug');

            if (!$video || !$video->is_available) {
                global $wp_query;
                $wp_query->set_404();
                status_header(404);
            }
        }

        if ($video_page === 'stream') {
            $video_id = get_query_var('video_id');

            if (!$video_id || !is_user_logged_in()) {
                global $wp_query;
                $wp_query->set_404();
                status_header(404);
            } else {
                $has_access = VideoBookingDB::has_video_access(get_current_user_id(), $video_id);
                if (!$has_access) {
                    wp_redirect(home_url('/my-account/my-recordings'));
                    exit;
                }
            }
        }
    }
}

// Initialize routing
VideoBookingRouting::init();

// Add body classes
add_filter('body_class', array('VideoBookingRouting', 'add_body_classes'));

// Handle 404s
add_action('template_redirect', array('VideoBookingRouting', 'handle_video_404'), 5);
