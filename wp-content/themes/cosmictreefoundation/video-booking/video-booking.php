<?php

/**
 * Video Booking System - Main Loader
 * 
 * A comprehensive WordPress-only secure video booking and streaming system
 * Features: Early bird pricing, coupon codes, secure streaming, access control
 * 
 * @package VideoBooking
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
define('VIDEO_BOOKING_VERSION', '1.1.0');
define('VIDEO_BOOKING_PATH', get_template_directory() . '/video-booking/');
define('VIDEO_BOOKING_URL', get_template_directory_uri() . '/video-booking/');
define('VIDEO_PRIVATE_PATH', WP_CONTENT_DIR . '/private-videos/');
define('VIDEO_PRIVATE_URL', WP_CONTENT_URL . '/private-videos/');

/**
 * Video Booking System Main Class
 */
class VideoBookingSystem
{

    private static $instance = null;

    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize the system
     */
    public function init()
    {
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }

        // Create database tables
        $this->create_tables();

        // Load includes
        $this->load_includes();

        // Add rewrite rules
        $this->add_rewrite_rules();

        // Create private video directory
        $this->create_private_directory();
    }

    /**
     * Load required files
     */
    private function load_includes()
    {
        $includes = array(
            'includes/database.php',
            'includes/routing.php',
            'includes/auth-system.php',
            'includes/cart-functions.php',
            'includes/payment-functions.php',
            'includes/coupon-functions.php',
            'includes/access-control.php',
            'includes/security.php',
            'includes/email-functions.php',
            'includes/video-stream.php',
            'includes/webhook-handler.php',
            'admin/video-admin.php',
            'admin/coupon-admin.php',
        );

        foreach ($includes as $file) {
            $file_path = VIDEO_BOOKING_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }

    /**
     * Create database tables
     */
    public function create_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Custom users table for video booking (separate from WordPress users)
        $users_table = $wpdb->prefix . 'video_booking_users';
        $users_sql = "CREATE TABLE $users_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            username varchar(60) NOT NULL,
            email varchar(100) NOT NULL,
            password varchar(255) NOT NULL,
            first_name varchar(50),
            last_name varchar(50),
            display_name varchar(250),
            phone varchar(20),
            city varchar(100),
            age int(3),
            status varchar(20) NOT NULL DEFAULT 'active',
            email_verified tinyint(1) NOT NULL DEFAULT 0,
            last_login datetime,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY username (username),
            UNIQUE KEY email (email),
            KEY status (status)
        ) $charset_collate;";

        // Videos table
        $videos_table = $wpdb->prefix . 'video_booking_videos';
        $videos_sql = "CREATE TABLE $videos_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description longtext,
            video_file varchar(255),
            video_source enum('private','media') NOT NULL DEFAULT 'private',
            thumbnail varchar(255),
            regular_price decimal(10,2) NOT NULL DEFAULT 0.00,
            early_bird_price decimal(10,2) NOT NULL DEFAULT 0.00,
            early_bird_note text,
            early_bird_end_date datetime,
            release_date datetime,
            duration_days int(11) NOT NULL DEFAULT 30,
            is_early_bird tinyint(1) NOT NULL DEFAULT 0,
            is_available tinyint(1) NOT NULL DEFAULT 1,
            upload_status enum('pending','uploaded','completed') NOT NULL DEFAULT 'pending',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY is_available (is_available),
            KEY is_early_bird (is_early_bird),
            KEY upload_status (upload_status),
            KEY video_source (video_source)
        ) $charset_collate;";

        // Orders table
        $orders_table = $wpdb->prefix . 'video_booking_orders';
        $orders_sql = "CREATE TABLE $orders_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            order_number varchar(50) NOT NULL,
            total_amount decimal(10,2) NOT NULL,
            coupon_code varchar(50),
            coupon_discount decimal(10,2) DEFAULT 0.00,
            payment_id varchar(100),
            payment_status enum('pending','completed','failed','refunded') NOT NULL DEFAULT 'pending',
            razorpay_order_id varchar(100),
            razorpay_payment_id varchar(100),
            razorpay_signature varchar(255),
            customer_name varchar(255) NOT NULL,
            customer_email varchar(255) NOT NULL,
            customer_phone varchar(20),
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY order_number (order_number),
            KEY user_id (user_id),
            KEY payment_status (payment_status),
            KEY razorpay_order_id (razorpay_order_id),
            KEY customer_email (customer_email)
        ) $charset_collate;";

        // Order items table
        $order_items_table = $wpdb->prefix . 'video_booking_order_items';
        $order_items_sql = "CREATE TABLE $order_items_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_id bigint(20) unsigned NOT NULL,
            video_id bigint(20) unsigned NOT NULL,
            price decimal(10,2) NOT NULL,
            is_early_bird tinyint(1) NOT NULL DEFAULT 0,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY video_id (video_id),
            FOREIGN KEY (order_id) REFERENCES $orders_table(id) ON DELETE CASCADE,
            FOREIGN KEY (video_id) REFERENCES $videos_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // User access table
        $access_table = $wpdb->prefix . 'video_booking_access';
        $access_sql = "CREATE TABLE $access_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            video_id bigint(20) unsigned NOT NULL,
            order_id bigint(20) unsigned NOT NULL,
            access_granted_at datetime,
            expires_at datetime,
            is_early_bird tinyint(1) NOT NULL DEFAULT 0,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_video (user_id, video_id),
            KEY user_id (user_id),
            KEY video_id (video_id),
            KEY expires_at (expires_at),
            KEY access_granted_at (access_granted_at),
            FOREIGN KEY (order_id) REFERENCES $orders_table(id) ON DELETE CASCADE,
            FOREIGN KEY (video_id) REFERENCES $videos_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Coupons table
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        $coupons_sql = "CREATE TABLE $coupons_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            code varchar(50) NOT NULL,
            description text,
            discount_type enum('flat','percentage') NOT NULL DEFAULT 'flat',
            discount_value decimal(10,2) NOT NULL,
            minimum_amount decimal(10,2) DEFAULT 0.00,
            usage_limit int(11) DEFAULT NULL,
            used_count int(11) NOT NULL DEFAULT 0,
            expires_at datetime,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY code (code),
            KEY is_active (is_active),
            KEY expires_at (expires_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($users_sql);
        dbDelta($videos_sql);
        dbDelta($orders_sql);
        dbDelta($order_items_sql);
        dbDelta($access_sql);
        dbDelta($coupons_sql);

        // Check if we need to add video_source column
        $current_version = get_option('video_booking_db_version', '0.0.0');
        if (version_compare($current_version, '1.0.0', '<')) {
            // Add video_source column if it doesn't exist
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $videos_table LIKE 'video_source'");
            if (empty($column_exists)) {
                $wpdb->query("ALTER TABLE $videos_table ADD COLUMN video_source enum('private','media') NOT NULL DEFAULT 'private' AFTER video_file");
                $wpdb->query("ALTER TABLE $videos_table ADD KEY video_source (video_source)");
            }
        }

        // Add release_date column if it doesn't exist (for early bird scheduling)
        if (version_compare($current_version, '1.1.0', '<')) {
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $videos_table LIKE 'release_date'");
            if (empty($column_exists)) {
                $wpdb->query("ALTER TABLE $videos_table ADD COLUMN release_date datetime AFTER early_bird_end_date");
            }
        }

        // Update database version
        update_option('video_booking_db_version', VIDEO_BOOKING_VERSION);
    }

    /**
     * Add rewrite rules for custom pages
     */
    public function add_rewrite_rules()
    {
        add_rewrite_rule('^workshop-recordings/?$', 'index.php?video_page=listing', 'top');
        add_rewrite_rule('^workshop-recordings/([^/]+)/?$', 'index.php?video_page=detail&video_slug=$matches[1]', 'top');
        add_rewrite_rule('^my-account/my-recordings/?$', 'index.php?video_page=my_recordings', 'top');
        add_rewrite_rule('^video-cart/?$', 'index.php?video_page=cart', 'top');
        add_rewrite_rule('^video-checkout/?$', 'index.php?video_page=checkout', 'top');
        add_rewrite_rule('^video-stream/([0-9]+)/?$', 'index.php?video_page=stream&video_id=$matches[1]', 'top');

        add_rewrite_tag('%video_page%', '([^&]+)');
        add_rewrite_tag('%video_slug%', '([^&]+)');
        add_rewrite_tag('%video_id%', '([0-9]+)');

        // Flush rewrite rules if needed
        if (get_option('video_booking_rewrite_flushed') !== VIDEO_BOOKING_VERSION) {
            flush_rewrite_rules();
            update_option('video_booking_rewrite_flushed', VIDEO_BOOKING_VERSION);
        }
    }

    /**
     * Create private video directory with security
     */
    public function create_private_directory()
    {
        if (!file_exists(VIDEO_PRIVATE_PATH)) {
            wp_mkdir_p(VIDEO_PRIVATE_PATH);

            // Create .htaccess to block direct access
            $htaccess_content = "# Block direct access to videos\n";
            $htaccess_content .= "Order Deny,Allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "# Allow only PHP scripts to access\n";
            $htaccess_content .= "<Files ~ \"\.php$\">\n";
            $htaccess_content .= "Allow from all\n";
            $htaccess_content .= "</Files>\n";

            file_put_contents(VIDEO_PRIVATE_PATH . '.htaccess', $htaccess_content);

            // Create index.php to prevent directory listing
            file_put_contents(VIDEO_PRIVATE_PATH . 'index.php', '<?php // Silence is golden');
        }
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts()
    {
        // Only enqueue on video booking pages
        $video_page = get_query_var('video_page');
        if (!$video_page && !is_page()) {
            return;
        }

        // Enqueue Razorpay script for checkout page
        if ($video_page === 'checkout') {
            wp_enqueue_script('razorpay', 'https://checkout.razorpay.com/v1/checkout.js', array(), null, true);
        }

        wp_enqueue_style('video-booking-style', VIDEO_BOOKING_URL . 'assets/css/video-booking.css', array(), VIDEO_BOOKING_VERSION);
        wp_enqueue_script('video-booking-js', VIDEO_BOOKING_URL . 'assets/js/video-booking.js', array('jquery'), VIDEO_BOOKING_VERSION, true);

        // Localize script with AJAX data
        wp_localize_script('video-booking-js', 'videoBooking', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('video_booking_nonce'),
            'razorpay_key' => defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '',
            'site_url' => home_url(),
        ));
    }

    /**
     * Activation hook
     */
    public function activate()
    {
        $this->create_tables();
        $this->create_private_directory();
        flush_rewrite_rules();
    }

    /**
     * Deactivation hook
     */
    public function deactivate()
    {
        flush_rewrite_rules();
    }
}

// Initialize the system
VideoBookingSystem::get_instance();

// Note: Template routing is handled in `includes/routing.php`.
