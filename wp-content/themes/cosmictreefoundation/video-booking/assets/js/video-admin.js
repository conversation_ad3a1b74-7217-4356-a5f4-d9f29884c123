/**
 * Video Booking Admin JavaScript
 *
 * @package VideoBooking
 */

jQuery(document).ready(function ($) {
  "use strict";

  // Video Admin Object
  const VideoAdmin = {
    // Initialize
    init: function () {
      this.bindEvents();
      this.initMediaUploader();
    },

    // Bind events
    bindEvents: function () {
      // Save video form
      $(document).on("submit", "#video-form", this.saveVideo);

      // Delete video
      $(document).on("click", ".delete-video", this.deleteVideo);

      // Toggle video status
      $(document).on("click", ".toggle-status", this.toggleStatus);

      // Schedule video release
      $(document).on("click", ".schedule-release", this.showScheduleModal);
      $(document).on("click", ".update-release", this.showScheduleModal);
      $(document).on("click", ".cancel-schedule", this.hideScheduleModal);
      $(document).on("submit", "#release-schedule-form", this.scheduleRelease);
      $(document).on(
        "change",
        "#schedule-immediate",
        this.toggleImmediateRelease
      );

      // Thumbnail upload
      $(document).on("click", ".upload-thumbnail", this.uploadThumbnail);
      $(document).on("click", ".remove-thumbnail", this.removeThumbnail);

      // Video upload
      $(document).on("click", ".upload-video", this.uploadVideo);
      $(document).on(
        "change",
        "#video_file_input",
        this.handleVideoFileSelected
      );

      // Release management
      $(document).on(
        "click",
        "#process-overdue-releases",
        this.processOverdueReleases
      );
      $(document).on(
        "click",
        "#check-pending-releases",
        this.checkPendingReleases
      );

      // Upload method tabs
      $(document).on("click", ".upload-method-tab", this.switchUploadMethod);

      // Media library buttons
      $(document).on("click", ".select-from-media", this.selectFromMedia);
      $(document).on("click", ".upload-to-media", this.uploadToMedia);
    },

    // Initialize media uploader
    initMediaUploader: function () {
      // Thumbnail uploader
      this.thumbnailUploader = wp.media({
        title: "Choose Thumbnail Image",
        button: {
          text: "Use this image",
        },
        multiple: false,
        library: {
          type: "image",
        },
      });

      this.thumbnailUploader.on("select", function () {
        const attachment = VideoAdmin.thumbnailUploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        $("#thumbnail").val(attachment.url);
        $(".thumbnail-preview").html(
          '<img src="' +
            attachment.url +
            '" alt="Thumbnail" style="max-width: 200px; height: auto;">'
        );
        $(".remove-thumbnail").show();
      });

      // Video uploader
      this.videoUploader = wp.media({
        title: "Choose Video File",
        button: {
          text: "Use this video",
        },
        multiple: false,
        library: {
          type: "video",
        },
      });

      this.videoUploader.on("select", function () {
        const attachment = VideoAdmin.videoUploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        $("#video_file").val(attachment.url);
        $("#video_source").val("media");
        $(".video-preview").html(
          "<p>Selected file: <strong>" +
            attachment.filename +
            "</strong></p>" +
            '<span class="video-source-indicator" style="color: #0073aa; font-size: 12px;">📁 From Media Library</span>'
        );
      });
    },

    // Save video
    saveVideo: function (e) {
      e.preventDefault();

      const $form = $(this);
      const $submitButton = $form.find('button[type="submit"]');
      const originalText = $submitButton.text();

      // Validate form
      if (!VideoAdmin.validateForm($form)) {
        return;
      }

      $submitButton.prop("disabled", true).text("Saving...");

      let formData = $form.serialize();
      formData += "&action=video_save";

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: formData,
        success: function (response) {
          if (response.success) {
            VideoAdmin.showMessage("Video saved successfully!", "success");

            // Redirect to videos list after 1 second
            setTimeout(function () {
              window.location.href = "admin.php?page=video-booking";
            }, 1000);
          } else {
            VideoAdmin.showMessage(
              response.data || "Failed to save video",
              "error"
            );
            $submitButton.prop("disabled", false).text(originalText);
          }
        },
        error: function () {
          VideoAdmin.showMessage("Network error. Please try again.", "error");
          $submitButton.prop("disabled", false).text(originalText);
        },
      });
    },

    // Delete video
    deleteVideo: function (e) {
      e.preventDefault();

      const $button = $(this);
      const videoId = $button.data("video-id");

      if (
        !confirm(
          "Are you sure you want to delete this video? This action cannot be undone."
        )
      ) {
        return;
      }

      $button.prop("disabled", true).text("Deleting...");

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: {
          action: "video_delete",
          nonce: videoAdmin.nonce,
          video_id: videoId,
        },
        success: function (response) {
          if (response.success) {
            $button.closest("tr").fadeOut(300, function () {
              $(this).remove();
            });
            VideoAdmin.showMessage("Video deleted successfully", "success");
          } else {
            VideoAdmin.showMessage(
              response.data || "Failed to delete video",
              "error"
            );
            $button.prop("disabled", false).text("Delete");
          }
        },
        error: function () {
          VideoAdmin.showMessage("Network error. Please try again.", "error");
          $button.prop("disabled", false).text("Delete");
        },
      });
    },

    // Toggle video status
    toggleStatus: function (e) {
      e.preventDefault();

      const $button = $(this);
      const videoId = $button.data("video-id");
      const $row = $button.closest("tr");

      $button.prop("disabled", true);

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: {
          action: "video_toggle_status",
          nonce: videoAdmin.nonce,
          video_id: videoId,
        },
        success: function (response) {
          if (response.success) {
            const newStatus = response.data.is_available;
            const $statusBadge = $row.find(".status-badge");

            if (newStatus) {
              $statusBadge
                .removeClass("status-inactive")
                .addClass("status-active")
                .text("Active");
              $button.text("Deactivate");
            } else {
              $statusBadge
                .removeClass("status-active")
                .addClass("status-inactive")
                .text("Inactive");
              $button.text("Activate");
            }

            VideoAdmin.showMessage("Video status updated", "success");
          } else {
            VideoAdmin.showMessage(
              response.data || "Failed to update status",
              "error"
            );
          }

          $button.prop("disabled", false);
        },
        error: function () {
          VideoAdmin.showMessage("Network error. Please try again.", "error");
          $button.prop("disabled", false);
        },
      });
    },

    // Show schedule release modal
    showScheduleModal: function (e) {
      e.preventDefault();

      const $button = $(this);
      const videoId = $button.data("video-id");
      const $row = $button.closest("tr");

      // Get current release date if updating
      const currentReleaseDate = $row.find(".release-info small").text();

      $("#schedule-video-id").val(videoId);

      // If updating, try to parse current date
      if (
        $button.hasClass("update-release") &&
        currentReleaseDate.includes("Scheduled:")
      ) {
        // This would need more sophisticated parsing in a real implementation
        $("#schedule-release-date").val("");
      } else {
        $("#schedule-release-date").val("");
      }

      $("#schedule-immediate").prop("checked", false);
      $("#release-schedule-modal").show();
    },

    // Hide schedule release modal
    hideScheduleModal: function (e) {
      if (e) e.preventDefault();
      $("#release-schedule-modal").hide();
    },

    // Toggle immediate release checkbox
    toggleImmediateRelease: function () {
      const isImmediate = $(this).is(":checked");
      $("#schedule-release-date").prop("required", !isImmediate);
    },

    // Schedule video release
    scheduleRelease: function (e) {
      e.preventDefault();

      const $form = $(this);
      const videoId = $("#schedule-video-id").val();
      const releaseDate = $("#schedule-release-date").val();
      const immediate = $("#schedule-immediate").is(":checked");

      if (!immediate && !releaseDate) {
        VideoAdmin.showMessage(
          "Please select a release date or check immediate release.",
          "error"
        );
        return;
      }

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: {
          action: "schedule_video_release",
          nonce: videoAdmin.nonce,
          video_id: videoId,
          release_date: immediate ? "" : releaseDate,
          immediate_release: immediate ? 1 : 0,
        },
        success: function (response) {
          if (response.success) {
            VideoAdmin.hideScheduleModal();
            VideoAdmin.showMessage(response.data.message, "success");

            // Refresh the page to show updated status
            setTimeout(() => {
              location.reload();
            }, 1500);
          } else {
            VideoAdmin.showMessage(response.data, "error");
          }
        },
        error: function () {
          VideoAdmin.showMessage(
            "Error scheduling video release. Please try again.",
            "error"
          );
        },
      });
    },

    // Upload thumbnail
    uploadThumbnail: function (e) {
      e.preventDefault();
      VideoAdmin.thumbnailUploader.open();
    },

    // Remove thumbnail
    removeThumbnail: function (e) {
      e.preventDefault();
      $("#thumbnail").val("");
      $(".thumbnail-preview").empty();
      $(this).hide();
    },

    // Switch upload method
    switchUploadMethod: function (e) {
      e.preventDefault();
      const $tab = $(this);
      const method = $tab.data("method");

      // Update tab states
      $(".upload-method-tab").removeClass("active");
      $tab.addClass("active");

      // Show/hide content
      $(".upload-method-content").hide();
      $("#" + method + "-upload-method").show();
    },

    // Select video from media library
    selectFromMedia: function (e) {
      e.preventDefault();
      VideoAdmin.videoUploader.open();
    },

    // Upload video to media library
    uploadToMedia: function (e) {
      e.preventDefault();

      // Create a new media uploader for video uploads
      const mediaUploader = wp.media({
        title: "Upload Video to Media Library",
        button: {
          text: "Use this video",
        },
        multiple: false,
        library: {
          type: "video",
        },
      });

      mediaUploader.on("select", function () {
        const attachment = mediaUploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        $("#video_file").val(attachment.url);
        $("#video_source").val("media");
        $(".video-preview").html(
          "<p>Selected file: <strong>" +
            attachment.filename +
            "</strong></p>" +
            '<span class="video-source-indicator" style="color: #0073aa; font-size: 12px;">📁 From Media Library</span>'
        );
      });

      mediaUploader.open();
    },

    // Upload video (open file picker and upload to private directory)
    uploadVideo: function (e) {
      e.preventDefault();
      $("#video_file_input").trigger("click");
    },

    handleVideoFileSelected: function (e) {
      const file = e.target.files[0];
      if (!file) return;

      // Basic size check (we will chunk regardless of size, but cap at 4GB for safety)
      const maxSize = 4 * 1024 * 1024 * 1024;
      if (file.size > maxSize) {
        alert("File too large. Max supported is 4GB.");
        return;
      }

      // Show progress UI
      $(".video-upload-progress").show();
      $("#video_upload_progress").val(0);
      $("#video_upload_status").text("Preparing...");

      // Initialize chunk session
      $.post(
        videoAdmin.ajax_url,
        {
          action: "video_chunk_init",
          nonce: videoAdmin.nonce,
          filename: file.name,
        },
        function (resp) {
          if (!resp || !resp.success) {
            alert((resp && resp.data) || "Failed to initialize upload");
            $(".video-upload-progress").hide();
            return;
          }

          // Set video source to private for direct uploads
          $("#video_source").val("private");
          const uploadId = resp.data.upload_id;
          const chunkSize = 8 * 1024 * 1024; // 8MB chunks
          const totalParts = Math.ceil(file.size / chunkSize);

          let index = 0;
          function sendNextChunk() {
            if (index >= totalParts) {
              // finalize
              $.post(
                videoAdmin.ajax_url,
                {
                  action: "video_chunk_finish",
                  nonce: videoAdmin.nonce,
                  upload_id: uploadId,
                  total_parts: totalParts,
                },
                function (finalResp) {
                  if (finalResp && finalResp.success) {
                    const filename = finalResp.data.filename;
                    $("#video_file").val(filename);
                    $(".video-preview").html(
                      "<p>Uploaded file: <strong>" +
                        filename +
                        "</strong></p>" +
                        '<span class="video-source-indicator" style="color: #d63638; font-size: 12px;">🔒 Private Upload</span>'
                    );
                    $("#video_upload_status").text("Upload complete");
                  } else {
                    alert(
                      (finalResp && finalResp.data) ||
                        "Failed to finalize upload"
                    );
                    $(".video-upload-progress").hide();
                  }
                }
              ).fail(function (jqXHR) {
                alert("Finalize failed: " + jqXHR.status);
                $(".video-upload-progress").hide();
              });
              return;
            }

            const start = index * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const blob = file.slice(start, end);

            const fd = new FormData();
            fd.append("action", "video_chunk_upload");
            fd.append("nonce", videoAdmin.nonce);
            fd.append("upload_id", uploadId);
            fd.append("index", index);
            fd.append("chunk", blob, file.name + ".part" + index);

            $.ajax({
              url: videoAdmin.ajax_url,
              type: "POST",
              data: fd,
              processData: false,
              contentType: false,
              success: function (r) {
                if (r && r.success) {
                  index++;
                  const percent = Math.round((index / totalParts) * 100);
                  $("#video_upload_progress").val(percent);
                  $("#video_upload_status").text(percent + "%");
                  sendNextChunk();
                } else {
                  alert((r && r.data) || "Chunk upload failed");
                  $(".video-upload-progress").hide();
                }
              },
              error: function (jqXHR) {
                let msg = "Network error during chunk upload.";
                if (jqXHR && jqXHR.status === 413) {
                  msg =
                    "Upload rejected by server (413). Trying fallback upload method...";
                  console.log("Chunked upload failed, trying fallback method");
                  VideoAdmin.tryFallbackUpload(file);
                  return;
                }
                alert(msg);
                $(".video-upload-progress").hide();
              },
            });
          }

          sendNextChunk();
        }
      ).fail(function (jqXHR) {
        if (jqXHR.status === 413) {
          console.log("Chunked upload init failed, trying fallback method");
          VideoAdmin.tryFallbackUpload(file);
        } else {
          alert("Init failed: " + jqXHR.status);
          $(".video-upload-progress").hide();
        }
      });
    },

    // Try fallback upload method
    tryFallbackUpload: function (file) {
      console.log("Attempting fallback upload for file:", file.name);

      $("#video_upload_status").text("Trying alternative upload method...");

      const formData = new FormData();
      formData.append("action", "video_upload_fallback");
      formData.append("nonce", videoAdmin.nonce);
      formData.append("video_file", file);

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        xhr: function () {
          const xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener(
            "progress",
            function (evt) {
              if (evt.lengthComputable) {
                const percentComplete = Math.round(
                  (evt.loaded / evt.total) * 100
                );
                $("#video_upload_progress").val(percentComplete);
                $("#video_upload_status").text(
                  percentComplete + "% (fallback method)"
                );
              }
            },
            false
          );
          return xhr;
        },
        success: function (response) {
          if (response && response.success) {
            const filename = response.data.filename;
            $("#video_file").val(filename);
            $("#video_source").val("private");
            $(".video-preview").html(
              "<p>Uploaded file: <strong>" +
                filename +
                "</strong></p>" +
                '<span class="video-source-indicator" style="color: #d63638; font-size: 12px;">🔒 Private Upload (Fallback)</span>'
            );
            $("#video_upload_status").text("Upload complete (fallback method)");
            VideoAdmin.showMessage(
              "Video uploaded successfully using fallback method!",
              "success"
            );
          } else {
            alert((response && response.data) || "Fallback upload failed");
            $(".video-upload-progress").hide();
          }
        },
        error: function (jqXHR) {
          let msg = "Fallback upload failed.";
          if (jqXHR.status === 413) {
            msg =
              "Upload rejected by server (413). File may be too large for server configuration.";
          }
          alert(msg);
          $(".video-upload-progress").hide();
        },
      });
    },

    // Validate form
    validateForm: function ($form) {
      let isValid = true;

      // Remove existing error messages
      $form.find(".error-message").remove();
      $form.find(".error").removeClass("error");

      // Required fields
      $form.find("[required]").each(function () {
        const $field = $(this);
        const value = $field.val().trim();

        if (!value) {
          VideoAdmin.showFieldError($field, "This field is required");
          isValid = false;
        }
      });

      // Price validation
      const regularPrice = parseFloat($("#regular_price").val());
      const earlyBirdPrice = parseFloat($("#early_bird_price").val());
      const isEarlyBird = $("#is_early_bird").is(":checked");

      if (regularPrice < 0) {
        VideoAdmin.showFieldError(
          $("#regular_price"),
          "Price cannot be negative"
        );
        isValid = false;
      }

      if (isEarlyBird && earlyBirdPrice >= regularPrice) {
        VideoAdmin.showFieldError(
          $("#early_bird_price"),
          "Early bird price must be less than regular price"
        );
        isValid = false;
      }

      // Duration validation
      const duration = parseInt($("#duration_days").val());
      if (duration < 1) {
        VideoAdmin.showFieldError(
          $("#duration_days"),
          "Duration must be at least 1 day"
        );
        isValid = false;
      }

      return isValid;
    },

    // Show field error
    showFieldError: function ($field, message) {
      $field.addClass("error");
      const $error = $(
        '<p class="error-message" style="color: #d63638; font-size: 13px; margin: 5px 0 0 0;">' +
          message +
          "</p>"
      );
      $field.after($error);
    },

    // Show message
    showMessage: function (message, type) {
      const $notice = $(
        '<div class="notice notice-' +
          type +
          ' is-dismissible"><p>' +
          message +
          "</p></div>"
      );

      // Remove existing notices
      $(".notice").remove();

      // Add new notice
      $(".wrap h1").after($notice);

      // Auto-hide success messages
      if (type === "success") {
        setTimeout(function () {
          $notice.fadeOut();
        }, 3000);
      }

      // Scroll to top
      $("html, body").animate({ scrollTop: 0 }, 300);
    },

    // Process overdue releases
    processOverdueReleases: function (e) {
      e.preventDefault();

      const $button = $(this);
      const $results = $("#release-management-results");

      $button.prop("disabled", true).text("Processing...");
      $results.html(
        '<div class="notice notice-info"><p>Processing overdue releases...</p></div>'
      );

      $.ajax({
        url: videoAdmin.ajax_url,
        type: "POST",
        data: {
          action: "process_overdue_releases",
          nonce: videoAdmin.nonce,
        },
        success: function (response) {
          if (response.success) {
            $results.html(
              '<div class="notice notice-success"><p>' +
                response.data.message +
                "</p></div>"
            );

            // Refresh the page if any releases were processed
            if (response.data.count > 0) {
              setTimeout(() => {
                location.reload();
              }, 2000);
            }
          } else {
            $results.html(
              '<div class="notice notice-error"><p>' +
                response.data +
                "</p></div>"
            );
          }
        },
        error: function () {
          $results.html(
            '<div class="notice notice-error"><p>Error processing overdue releases. Please try again.</p></div>'
          );
        },
        complete: function () {
          $button.prop("disabled", false).text("Process Overdue Releases");
        },
      });
    },

    // Check pending releases
    checkPendingReleases: function (e) {
      e.preventDefault();

      const $button = $(this);
      const $results = $("#release-management-results");

      $button.prop("disabled", true).text("Checking...");
      $results.html(
        '<div class="notice notice-info"><p>Checking for pending releases...</p></div>'
      );

      // This would require a separate AJAX endpoint to check pending releases
      // For now, just show a message
      setTimeout(() => {
        $results.html(
          '<div class="notice notice-info"><p>Check the video list above for videos with "Pending" status that have passed their release date.</p></div>'
        );
        $button.prop("disabled", false).text("Check Pending Releases");
      }, 1000);
    },
  };

  // Initialize Video Admin
  VideoAdmin.init();

  // Make VideoAdmin globally available
  window.VideoAdmin = VideoAdmin;
});
