<?php

/**
 * Video Streaming Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    $current_url = home_url($_SERVER['REQUEST_URI']);
    $login_url = method_exists('VideoBookingAuth', 'get_login_url') ? VideoBookingAuth::get_login_url($current_url) : home_url('/video-user-login?redirect_to=' . urlencode($current_url));
    wp_redirect($login_url);
    exit;
}

// Get video ID from URL
$video_id = get_query_var('video_id');
if (!$video_id) {
    wp_redirect(home_url('/my-account/my-recordings'));
    exit;
}

$user_id = get_current_user_id();

// Check access
if (!VideoBookingDB::has_video_access($user_id, $video_id)) {
    wp_redirect(home_url('/my-account/my-recordings'));
    exit;
}

// Get video details
$video = VideoBookingDB::get_video($video_id);
if (!$video) {
    wp_redirect(home_url('/my-account/my-recordings'));
    exit;
}

// Get user access details
$access = VideoBookingDB::get_user_access($user_id, $video_id);
$streaming_url = VideoBookingStream::get_streaming_url($video_id, $user_id);

get_header();
?>

<div class="video-streaming-container">
    <div class="video-player-wrapper">
        <div class="video-header">
            <div class="video-title-section">
                <h1><?php echo esc_html($video->title); ?></h1>
                <div class="video-meta">
                    <?php if ($access->expires_at): ?>
                        <?php
                        $days_remaining = ceil((strtotime($access->expires_at) - current_time('timestamp')) / (24 * 60 * 60));
                        ?>
                        <span class="access-info">
                            <i class="fa fa-clock-o"></i>
                            <?php echo $days_remaining > 0 ? $days_remaining . ' days remaining' : 'Access expires soon'; ?>
                        </span>
                    <?php endif; ?>

                    <?php if ($access->is_early_bird): ?>
                        <span class="early-bird-indicator">
                            <i class="fa fa-star"></i>
                            Early Bird Purchase
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="video-actions">
                <a href="<?php echo home_url('/my-account/my-recordings'); ?>" class="btn btn-secondary">
                    <i class="fa fa-arrow-left"></i>
                    Back to My Recordings
                </a>
            </div>
        </div>

        <div class="video-player-container" id="video-container">
            <div class="video-loading">
                <div class="loading-spinner"></div>
                <p>Loading video...</p>
            </div>

            <video id="secure-video-player"
                class="video-player"
                controls
                controlsList="nodownload"
                disablePictureInPicture
                preload="metadata"
                style="display: none;">
                <source src="<?php echo esc_url($streaming_url); ?>" type="video/mp4">
                Your browser does not support the video tag.
            </video>

            <div class="video-error" id="video-error" style="display: none;">
                <div class="error-icon">⚠️</div>
                <h3>Video Unavailable</h3>
                <p>There was an error loading the video. Please try refreshing the page.</p>
                <button class="btn btn-primary" onclick="location.reload()">Refresh Page</button>
            </div>
        </div>

        <?php if ($video->description): ?>
            <div class="video-description">
                <h3>About This Workshop</h3>
                <div class="description-content">
                    <?php echo wp_kses_post(wpautop($video->description)); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="video-info-panel">
            <div class="info-section">
                <h4>Access Information</h4>
                <ul class="info-list">
                    <li>
                        <i class="fa fa-calendar"></i>
                        Purchased: <?php echo date('M j, Y', strtotime($access->created_at)); ?>
                    </li>
                    <?php if ($access->expires_at): ?>
                        <li>
                            <i class="fa fa-clock-o"></i>
                            Expires: <?php echo date('M j, Y g:i A', strtotime($access->expires_at)); ?>
                        </li>
                    <?php endif; ?>
                    <li>
                        <i class="fa fa-shield"></i>
                        Secure streaming with access control
                    </li>
                </ul>
            </div>

            <div class="info-section">
                <h4>Viewing Guidelines</h4>
                <ul class="info-list">
                    <li>
                        <i class="fa fa-info-circle"></i>
                        This video is for your personal use only
                    </li>
                    <li>
                        <i class="fa fa-ban"></i>
                        Sharing or downloading is not permitted
                    </li>
                    <li>
                        <i class="fa fa-mobile"></i>
                        Watch on any device with your account
                    </li>
                    <li>
                        <i class="fa fa-support"></i>
                        Contact support if you experience issues
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
    /* Video Streaming Styles */
    .video-streaming-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #000;
        min-height: 100vh;
    }

    .video-player-wrapper {
        background: #111;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .video-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25px 30px;
        background: #1a1a1a;
        border-bottom: 1px solid #333;
    }

    .video-title-section h1 {
        color: white;
        margin: 0 0 10px 0;
        font-size: 1.8em;
        font-weight: bold;
    }

    .video-meta {
        display: flex;
        gap: 20px;
        align-items: center;
    }

    .access-info {
        color: #28a745;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .early-bird-indicator {
        background: #ff6b35;
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .video-actions .btn {
        background: #2c5aa0;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }

    .video-actions .btn:hover {
        background: #1e3f73;
        color: white;
    }

    /* Video Player */
    .video-player-container {
        position: relative;
        width: 100%;
        min-height: 500px;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .video-player {
        width: 100%;
        height: auto;
        max-height: 70vh;
        background: #000;
    }

    .video-loading {
        text-align: center;
        color: white;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #333;
        border-top: 4px solid #2c5aa0;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .video-error {
        text-align: center;
        color: white;
        padding: 40px;
    }

    .error-icon {
        font-size: 3em;
        margin-bottom: 20px;
    }

    .video-error h3 {
        margin-bottom: 15px;
        color: #dc3545;
    }

    .video-error p {
        margin-bottom: 25px;
        color: #ccc;
    }

    /* Video Description */
    .video-description {
        padding: 30px;
        background: #1a1a1a;
        border-bottom: 1px solid #333;
    }

    .video-description h3 {
        color: white;
        margin-bottom: 20px;
        font-size: 1.3em;
    }

    .description-content {
        color: #ccc;
        line-height: 1.7;
    }

    .description-content p {
        margin-bottom: 15px;
    }

    /* Info Panel */
    .video-info-panel {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        padding: 30px;
        background: #1a1a1a;
    }

    .info-section h4 {
        color: white;
        margin-bottom: 20px;
        font-size: 1.1em;
        border-bottom: 2px solid #2c5aa0;
        padding-bottom: 10px;
    }

    .info-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .info-list li {
        color: #ccc;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        line-height: 1.5;
    }

    .info-list i {
        color: #2c5aa0;
        width: 16px;
        flex-shrink: 0;
    }

    /* Security Overlay */
    .video-player-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 1;
        background: transparent;
    }

    /* Disable text selection on video */
    .video-player-container {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .video-streaming-container {
            padding: 10px;
        }

        .video-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
            padding: 20px;
        }

        .video-title-section h1 {
            font-size: 1.5em;
        }

        .video-meta {
            flex-direction: column;
            gap: 10px;
        }

        .video-info-panel {
            grid-template-columns: 1fr;
            gap: 20px;
            padding: 20px;
        }

        .video-player-container {
            min-height: 300px;
        }

        .video-description {
            padding: 20px;
        }
    }

    /* Dark theme for better video viewing */
    body.video-streaming {
        background: #000;
        color: white;
    }

    /* Hide WordPress admin bar for better viewing */
    .video-streaming #wpadminbar {
        display: none;
    }

    .video-streaming html {
        margin-top: 0 !important;
    }
</style>

<script>
    jQuery(document).ready(function($) {
        // Add video streaming class to body
        $('body').addClass('video-streaming');

        const video = document.getElementById('secure-video-player');
        const container = document.getElementById('video-container');
        const loading = $('.video-loading');
        const errorDiv = $('#video-error');

        // Video loading handlers
        video.addEventListener('loadstart', function() {
            loading.show();
            errorDiv.hide();
        });

        video.addEventListener('canplay', function() {
            loading.hide();
            $(video).show();
        });

        video.addEventListener('error', function() {
            loading.hide();
            $(video).hide();
            errorDiv.show();
            console.error('Video loading error:', video.error);
        });

        // Security measures

        // Disable right-click on video
        $(video).on('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // Disable common keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S
            if (e.keyCode === 123 ||
                (e.ctrlKey && e.shiftKey && e.keyCode === 73) ||
                (e.ctrlKey && e.keyCode === 85) ||
                (e.ctrlKey && e.keyCode === 83)) {
                e.preventDefault();
                return false;
            }
        });

        // Disable drag and drop
        $(video).on('dragstart', function(e) {
            e.preventDefault();
            return false;
        });

        // Monitor for screen recording software (basic detection)
        let isRecording = false;

        // Check for common screen recording indicators
        function checkForRecording() {
            // Check if page is being captured
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                // This is a basic check - more sophisticated detection would be needed for production
                console.log('Screen capture API available - monitoring for usage');
            }

            // Check for unusual activity patterns
            if (document.hidden && video.currentTime > 0 && !video.paused) {
                console.warn('Video playing while page is hidden - possible recording');
            }
        }

        // Monitor video events
        video.addEventListener('play', function() {
            console.log('Video playback started');
            setInterval(checkForRecording, 5000);
        });

        video.addEventListener('pause', function() {
            console.log('Video playback paused');
        });

        // Prevent video download attempts
        video.addEventListener('loadedmetadata', function() {
            // Remove download attribute if somehow added
            video.removeAttribute('download');
        });

        // Log viewing session
        let sessionStartTime = Date.now();
        let totalWatchTime = 0;

        video.addEventListener('timeupdate', function() {
            if (!video.paused) {
                totalWatchTime += 1; // Approximate seconds watched
            }
        });

        // Send analytics when user leaves
        window.addEventListener('beforeunload', function() {
            const sessionDuration = Date.now() - sessionStartTime;

            // Send viewing analytics (you can implement this)
            console.log('Session analytics:', {
                videoId: <?php echo $video_id; ?>,
                sessionDuration: sessionDuration,
                totalWatchTime: totalWatchTime,
                completionPercentage: video.duration ? (video.currentTime / video.duration * 100) : 0
            });
        });

        // Fullscreen handling
        video.addEventListener('fullscreenchange', function() {
            if (document.fullscreenElement) {
                console.log('Video entered fullscreen');
            } else {
                console.log('Video exited fullscreen');
            }
        });
    });
</script>

<?php get_footer(); ?>