<?php

/**
 * Video Cart Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

$cart = VideoBookingCart::get_cart();
$cart_summary = VideoBookingCart::get_cart_summary();
$cart_errors = VideoBookingCart::validate_cart();
?>

<div class="video-booking-container">
    <div class="cart-container">
        <div class="cart-header">
            <h1>Your Cart</h1>
            <a href="<?php echo home_url('/workshop-recordings'); ?>" class="continue-shopping">
                <i class="fa fa-arrow-left"></i> Continue Shopping
            </a>
        </div>

        <?php if (!empty($cart_errors)): ?>
            <div class="cart-errors">
                <h3>Cart Issues:</h3>
                <ul>
                    <?php foreach ($cart_errors as $error): ?>
                        <li><?php echo esc_html($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button class="btn btn-secondary refresh-cart">Refresh Cart</button>
            </div>
        <?php endif; ?>

        <?php if (empty($cart)): ?>
            <div class="empty-cart">
                <div class="empty-cart-icon">
                    <i class="fa fa-shopping-cart"></i>
                </div>
                <h2>Your cart is empty</h2>
                <p>Discover our amazing workshop recordings and start learning today!</p>
                <a href="<?php echo home_url('/workshop-recordings'); ?>" class="btn btn-primary">
                    Browse Videos
                </a>
            </div>
        <?php else: ?>
            <div class="cart-content">
                <div class="cart-items">
                    <div class="cart-items-header">
                        <h2>Items in Your Cart (<?php echo count($cart); ?>)</h2>
                    </div>

                    <?php foreach ($cart as $cart_key => $item): ?>
                        <div class="cart-item" data-cart-key="<?php echo esc_attr($cart_key); ?>">
                            <div class="cart-item-thumbnail">
                                <?php if ($item['thumbnail']): ?>
                                    <img src="<?php echo esc_url($item['thumbnail']); ?>" alt="<?php echo esc_attr($item['title']); ?>">
                                <?php else: ?>
                                    <div class="no-thumbnail">
                                        <i class="fa fa-play-circle"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="cart-item-info">
                                <h3 class="cart-item-title"><?php echo esc_html($item['title']); ?></h3>

                                <?php if ($item['is_early_bird']): ?>
                                    <div class="cart-item-type">
                                        <span class="early-bird-badge">Early Bird</span>
                                        <small>Available after workshop completion</small>
                                    </div>
                                <?php else: ?>
                                    <div class="cart-item-type">
                                        <span class="instant-access-badge">Instant Access</span>
                                    </div>
                                <?php endif; ?>

                                <div class="cart-item-meta">
                                    <span>Added: <?php echo date('M j, Y', strtotime($item['added_at'])); ?></span>
                                </div>
                            </div>

                            <div class="cart-item-price">
                                <span class="price">₹<?php echo number_format($item['price'], 2); ?></span>
                            </div>

                            <div class="cart-item-actions">
                                <button class="cart-item-remove"
                                    data-video-id="<?php echo $item['video_id']; ?>"
                                    data-early-bird="<?php echo $item['is_early_bird'] ? 1 : 0; ?>"
                                    title="Remove from cart">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="cart-sidebar">
                    <!-- Cart Summary -->
                    <div class="cart-summary">
                        <h3>Order Summary</h3>

                        <div class="summary-line">
                            <span>Subtotal:</span>
                            <span class="subtotal">₹<?php echo $cart_summary['subtotal']; ?></span>
                        </div>

                        <?php if ($cart_summary['discount'] > 0): ?>
                            <div class="summary-line discount-line">
                                <span>Discount:</span>
                                <span class="discount">-₹<?php echo $cart_summary['discount']; ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="summary-line total-line">
                            <span><strong>Total:</strong></span>
                            <span class="total"><strong>₹<?php echo $cart_summary['total']; ?></strong></span>
                        </div>

                        <div class="checkout-actions">
                            <a href="<?php echo home_url('/video-checkout'); ?>" class="btn btn-success btn-full-width checkout-btn">
                                <i class="fa fa-lock"></i>
                                Proceed to Checkout
                            </a>

                            <div class="security-info">
                                <i class="fa fa-shield"></i>
                                <span>Secure checkout with 256-bit SSL encryption</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="payment-methods">
                        <h4>We Accept</h4>
                        <div class="payment-icons">
                            <i class="fa fa-cc-visa"></i>
                            <i class="fa fa-cc-mastercard"></i>
                            <i class="fa fa-cc-amex"></i>
                            <span class="razorpay-text">Razorpay</span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    /* Cart Styles */
    .cart-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
    }

    .cart-header h1 {
        margin: 0;
        color: #333;
    }

    .continue-shopping {
        color: #2c5aa0;
        text-decoration: none;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .continue-shopping:hover {
        text-decoration: underline;
    }

    /* Cart Errors */
    .cart-errors {
        background: #f8d7da;
        color: #721c24;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }

    .cart-errors h3 {
        margin-top: 0;
    }

    .cart-errors ul {
        margin: 15px 0;
        padding-left: 20px;
    }

    /* Empty Cart */
    .empty-cart {
        text-align: center;
        padding: 80px 20px;
        background: #f8f9fa;
        border-radius: 12px;
    }

    .empty-cart-icon {
        font-size: 4em;
        color: #ccc;
        margin-bottom: 20px;
    }

    .empty-cart h2 {
        color: #666;
        margin-bottom: 15px;
    }

    .empty-cart p {
        color: #888;
        font-size: 1.1em;
        margin-bottom: 30px;
    }

    /* Cart Content */
    .cart-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
    }

    .cart-items-header {
        margin-bottom: 20px;
    }

    .cart-items-header h2 {
        color: #333;
        margin: 0;
    }

    /* Cart Items */
    .cart-item {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .cart-item-thumbnail {
        width: 100px;
        height: 75px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
    }

    .cart-item-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumbnail {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #f0f0f0;
        color: #999;
    }

    .cart-item-info {
        flex: 1;
    }

    .cart-item-title {
        margin: 0 0 10px 0;
        font-size: 1.1em;
        color: #333;
    }

    .cart-item-type {
        margin-bottom: 8px;
    }

    .early-bird-badge {
        background: #ff6b35;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .instant-access-badge {
        background: #28a745;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .cart-item-meta {
        font-size: 13px;
        color: #666;
    }

    .cart-item-price {
        font-size: 1.3em;
        font-weight: bold;
        color: #2c5aa0;
    }

    .cart-item-actions {
        flex-shrink: 0;
    }

    .cart-item-remove {
        background: #dc3545;
        color: white;
        border: none;
        padding: 10px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s ease;
    }

    .cart-item-remove:hover {
        background: #c82333;
    }

    /* Cart Sidebar */
    .cart-sidebar {
        display: flex;
        flex-direction: column;
        gap: 25px;
    }

    .coupon-section,
    .cart-summary,
    .payment-methods {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .coupon-section h3,
    .cart-summary h3 {
        margin: 0 0 20px 0;
        color: #333;
    }

    .coupon-form {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
    }

    .coupon-input {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
    }

    .applied-coupon {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
    }

    .coupon-info strong {
        color: #155724;
    }

    .coupon-discount {
        color: #28a745;
        font-weight: bold;
    }

    .coupon-message {
        min-height: 20px;
        font-size: 14px;
        margin-top: 5px;
    }

    /* Cart Summary */
    .summary-line {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }

    .discount-line {
        color: #28a745;
    }

    .total-line {
        border-top: 2px solid #eee;
        padding-top: 15px;
        font-size: 1.2em;
        color: #2c5aa0;
    }

    .checkout-actions {
        margin-top: 20px;
    }

    .security-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 15px;
        font-size: 13px;
        color: #666;
        justify-content: center;
    }

    .security-info i {
        color: #28a745;
    }

    /* Payment Methods */
    .payment-methods h4 {
        margin: 0 0 15px 0;
        color: #333;
    }

    .payment-icons {
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 1.5em;
        color: #666;
    }

    .razorpay-text {
        font-size: 0.8em;
        font-weight: bold;
        color: #2c5aa0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .cart-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .cart-item {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .cart-item-thumbnail {
            width: 120px;
            height: 90px;
        }

        .coupon-form {
            flex-direction: column;
        }

        .applied-coupon {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }
    }
</style>

<script>
    jQuery(document).ready(function($) {

        // Refresh cart
        $('.refresh-cart').click(function() {
            location.reload();
        });

        // Remove from cart
        $('.cart-item-remove').click(function(e) {
            e.preventDefault();

            const $button = $(this);
            const videoId = $button.data('video-id');
            const isEarlyBird = $button.data('early-bird');

            $button.prop('disabled', true);

            $.ajax({
                url: videoBooking.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_remove_from_cart',
                    nonce: videoBooking.nonce,
                    video_id: videoId,
                    is_early_bird: isEarlyBird
                },
                success: function(response) {
                    if (response.success) {
                        $button.closest('.cart-item').fadeOut(300, function() {
                            $(this).remove();
                            // Reload page to update cart summary
                            location.reload();
                        });
                    } else {
                        alert(response.data || 'Failed to remove video');
                        $button.prop('disabled', false);
                    }
                },
                error: function() {
                    alert('Network error. Please try again.');
                    $button.prop('disabled', false);
                }
            });
        });
    });
</script>

<?php get_footer(); ?>