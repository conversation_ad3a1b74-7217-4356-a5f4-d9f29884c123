<?php

/**
 * My Recordings Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    $login_url = method_exists('VideoBookingAuth', 'get_login_url') ? VideoBookingAuth::get_login_url(home_url('/my-account/my-recordings')) : home_url('/video-user-login?redirect_to=' . urlencode(home_url('/my-account/my-recordings')));
    wp_redirect($login_url);
    exit;
}

get_header();

$user_id = get_current_user_id();

// Process any overdue video releases before displaying
VideoBookingAccessControl::process_overdue_releases();

$user_access = VideoBookingDB::get_user_access($user_id);
$order_completed = isset($_GET['order_completed']) && $_GET['order_completed'] == '1';
?>

<div class="video-booking-container">
    <div class="my-recordings-container">
        <div class="my-recordings-header">
            <h1>My Recordings</h1>
            <p>Access your purchased workshop recordings</p>

            <?php if ($order_completed): ?>
                <div class="order-success-message">
                    <i class="fa fa-check-circle"></i>
                    <strong>Order completed successfully!</strong>
                    <p>Your videos are now available below. Check your email for order confirmation.</p>
                </div>
            <?php endif; ?>
        </div>

        <?php if (empty($user_access)): ?>
            <div class="no-recordings">
                <div class="no-recordings-icon">
                    <i class="fa fa-video-camera"></i>
                </div>
                <h2>No Recordings Yet</h2>
                <p>You haven't purchased any workshop recordings yet. Browse our collection to start learning!</p>
                <a href="<?php echo home_url('/workshop-recordings'); ?>" class="btn btn-primary">
                    Browse Workshop Recordings
                </a>
            </div>
        <?php else: ?>
            <div class="recordings-grid">
                <?php foreach ($user_access as $access): ?>
                    <?php
                    // Determine video status based on access and release date
                    $current_timestamp = current_time('timestamp');
                    $has_access_granted = !empty($access->access_granted_at);
                    $is_expired = $access->expires_at && strtotime($access->expires_at) < $current_timestamp;

                    // For early bird videos, check if release date has passed
                    $is_released = true; // Default for regular videos
                    $release_date_text = '';
                    $should_have_access = false; // New flag to check if access should be granted

                    if ($access->is_early_bird && !empty($access->release_date)) {
                        $release_timestamp = strtotime($access->release_date);
                        $is_released = $release_timestamp <= $current_timestamp;

                        // Check if video should have access based on release date (regardless of cron processing)
                        $should_have_access = $is_released;

                        if (!$is_released) {
                            $release_date_text = date('M j, Y g:i A', $release_timestamp);
                        }
                    } else {
                        // For regular videos, they should have access if purchased
                        $should_have_access = true;
                    }

                    // Determine final status - prioritize should_have_access over has_access_granted
                    $is_accessible = ($has_access_granted || $should_have_access) && $is_released && !$is_expired;
                    $is_pending = !$should_have_access || ($access->is_early_bird && !$is_released);

                    $days_remaining = 0;
                    if ($access->expires_at && $is_accessible) {
                        // If access should be granted but hasn't been processed yet, calculate from now
                        if ($should_have_access && !$has_access_granted && $access->is_early_bird) {
                            // Calculate expiry from current time for overdue releases
                            $video = VideoBookingDB::get_video($access->video_id);
                            if ($video) {
                                $theoretical_expiry = strtotime("+{$video->duration_days} days");
                                $days_remaining = ceil(($theoretical_expiry - $current_timestamp) / (24 * 60 * 60));
                            }
                        } else {
                            $days_remaining = ceil((strtotime($access->expires_at) - $current_timestamp) / (24 * 60 * 60));
                        }
                    }
                    ?>

                    <div class="recording-card <?php echo $is_expired ? 'expired' : ($is_pending ? 'pending' : 'accessible'); ?>">
                        <div class="recording-thumbnail">
                            <?php if ($access->thumbnail): ?>
                                <img src="<?php echo esc_url($access->thumbnail); ?>" alt="<?php echo esc_attr($access->title); ?>">
                            <?php else: ?>
                                <div class="no-thumbnail">
                                    <i class="fa fa-play-circle"></i>
                                </div>
                            <?php endif; ?>

                            <!-- Status Overlay -->
                            <div class="status-overlay">
                                <?php if ($is_expired): ?>
                                    <div class="status-badge expired">
                                        <i class="fa fa-clock-o"></i>
                                        Expired
                                    </div>
                                <?php elseif ($is_pending): ?>
                                    <div class="status-badge pending">
                                        <i class="fa fa-hourglass-half"></i>
                                        Coming Soon
                                    </div>
                                <?php else: ?>
                                    <div class="status-badge accessible">
                                        <i class="fa fa-play"></i>
                                        Watch Now
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="recording-info">
                            <h3 class="recording-title"><?php echo esc_html($access->title); ?></h3>

                            <div class="recording-meta">
                                <?php if ($access->is_early_bird): ?>
                                    <span class="early-bird-indicator">
                                        <i class="fa fa-star"></i>
                                        Early Bird Purchase
                                    </span>
                                <?php endif; ?>

                                <div class="access-info">
                                    <?php if ($is_expired): ?>
                                        <span class="expired-text">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            Access expired on <?php echo date('M j, Y', strtotime($access->expires_at)); ?>
                                        </span>
                                    <?php elseif ($is_pending): ?>
                                        <span class="pending-text">
                                            <i class="fa fa-info-circle"></i>
                                            <?php if ($access->is_early_bird && $release_date_text): ?>
                                                Video will be available on <?php echo $release_date_text; ?>
                                            <?php else: ?>
                                                Video will be available after workshop completion
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="access-text">
                                            <i class="fa fa-calendar"></i>
                                            <?php if ($days_remaining > 0): ?>
                                                <?php echo $days_remaining; ?> days remaining
                                                <?php if ($should_have_access && !$has_access_granted): ?>
                                                    <small style="display: block; color: #666; font-style: italic;">
                                                        (Processing video access...)
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                Lifetime access
                                                <?php if ($should_have_access && !$has_access_granted): ?>
                                                    <small style="display: block; color: #666; font-style: italic;">
                                                        (Processing video access...)
                                                    </small>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="purchase-date">
                                    <i class="fa fa-shopping-cart"></i>
                                    Purchased: <?php echo date('M j, Y', strtotime($access->created_at)); ?>
                                </div>
                            </div>

                            <div class="recording-actions">
                                <?php if ($is_accessible): ?>
                                    <?php if ($should_have_access && !$has_access_granted): ?>
                                        <!-- Video should be accessible but access hasn't been processed yet -->
                                        <a href="<?php echo home_url('/video-stream/' . $access->video_id . '?force_check=1'); ?>" class="btn btn-primary btn-full-width">
                                            <i class="fa fa-play"></i>
                                            Watch Video
                                        </a>
                                    <?php else: ?>
                                        <a href="<?php echo home_url('/video-stream/' . $access->video_id); ?>" class="btn btn-primary btn-full-width">
                                            <i class="fa fa-play"></i>
                                            Watch Video
                                        </a>
                                    <?php endif; ?>
                                <?php elseif ($is_expired): ?>
                                    <div class="expired-actions">
                                        <p class="expired-message">Access expired. Please repurchase the video.</p>
                                        <a href="<?php echo home_url('/workshop-recordings'); ?>" class="btn btn-secondary btn-full-width">
                                            <i class="fa fa-refresh"></i>
                                            Repurchase Video
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="pending-actions">
                                        <p class="pending-message">
                                            <?php if ($access->is_early_bird && $release_date_text): ?>
                                                Video will be available on <?php echo $release_date_text; ?>
                                            <?php else: ?>
                                                Video will be available soon. We'll notify you via email.
                                            <?php endif; ?>
                                        </p>
                                        <button class="btn btn-secondary btn-full-width" disabled>
                                            <i class="fa fa-hourglass-half"></i>
                                            Coming Soon
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Account Actions -->
            <div class="account-actions">
                <div class="action-card">
                    <h3>Need More Videos?</h3>
                    <p>Explore our complete collection of workshop recordings</p>
                    <a href="<?php echo home_url('/workshop-recordings'); ?>" class="btn btn-primary">
                        Browse All Videos
                    </a>
                </div>

                <div class="action-card">
                    <h3>Account Settings</h3>
                    <p>Update your profile and preferences</p>
                    <a href="<?php echo wp_logout_url(home_url()); ?>" class="btn btn-secondary">
                        Logout
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    /* My Recordings Styles */
    .my-recordings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .my-recordings-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
        color: white;
        border-radius: 12px;
    }

    .my-recordings-header h1 {
        font-size: 2.5em;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .my-recordings-header p {
        font-size: 1.1em;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .order-success-message {
        background: #d4edda;
        color: #155724;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        text-align: left;
    }

    .order-success-message i {
        color: #28a745;
        margin-right: 10px;
        font-size: 1.2em;
    }

    .order-success-message p {
        margin: 5px 0 0 0;
        opacity: 1;
    }

    /* No Recordings */
    .no-recordings {
        text-align: center;
        padding: 80px 20px;
        background: #f8f9fa;
        border-radius: 12px;
    }

    .no-recordings-icon {
        font-size: 4em;
        color: #ccc;
        margin-bottom: 20px;
    }

    .no-recordings h2 {
        color: #666;
        margin-bottom: 15px;
    }

    .no-recordings p {
        color: #888;
        font-size: 1.1em;
        margin-bottom: 30px;
    }

    /* Recordings Grid */
    .recordings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    .recording-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .recording-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .recording-card.expired {
        opacity: 0.7;
        border: 2px solid #dc3545;
    }

    .recording-card.pending {
        border: 2px solid #ffc107;
    }

    .recording-card.accessible {
        border: 2px solid #28a745;
    }

    .recording-thumbnail {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;
    }

    .recording-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumbnail {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #f0f0f0;
        color: #999;
        font-size: 3em;
    }

    .status-overlay {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .status-badge {
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .status-badge.expired {
        background: #dc3545;
        color: white;
    }

    .status-badge.pending {
        background: #ffc107;
        color: #212529;
    }

    .status-badge.accessible {
        background: #28a745;
        color: white;
    }

    .recording-info {
        padding: 25px;
    }

    .recording-title {
        font-size: 1.3em;
        margin-bottom: 20px;
        color: #333;
        line-height: 1.4;
    }

    .recording-meta {
        margin-bottom: 25px;
    }

    .early-bird-indicator {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 15px;
    }

    .access-info,
    .purchase-date {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .expired-text {
        color: #dc3545;
        font-weight: bold;
    }

    .pending-text {
        color: #856404;
        font-weight: bold;
    }

    .access-text {
        color: #28a745;
        font-weight: bold;
    }

    .purchase-date {
        color: #666;
    }

    .recording-actions {
        margin-top: 20px;
    }

    .expired-actions,
    .pending-actions {
        text-align: center;
    }

    .expired-message,
    .pending-message {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    /* Account Actions */
    .account-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 50px;
    }

    .action-card {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .action-card h3 {
        margin-bottom: 15px;
        color: #333;
    }

    .action-card p {
        color: #666;
        margin-bottom: 25px;
        line-height: 1.6;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .my-recordings-header h1 {
            font-size: 2em;
        }

        .my-recordings-header p {
            font-size: 1em;
        }

        .recordings-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .recording-card {
            margin-bottom: 20px;
        }

        .account-actions {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .status-overlay {
            top: 10px;
            right: 10px;
        }

        .status-badge {
            padding: 6px 10px;
            font-size: 11px;
        }
    }

    /* Animation for new purchases */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .recording-card {
        animation: slideInUp 0.6s ease-out;
    }
</style>

<script>
    jQuery(document).ready(function($) {
        // Auto-hide success message after 10 seconds
        setTimeout(function() {
            $('.order-success-message').fadeOut(500);
        }, 10000);

        // Add click tracking for video access
        $('.recording-card.accessible .btn').click(function() {
            // Track video access analytics if needed
            console.log('Video accessed:', $(this).closest('.recording-card').find('.recording-title').text());
        });
    });
</script>

<?php get_footer(); ?>