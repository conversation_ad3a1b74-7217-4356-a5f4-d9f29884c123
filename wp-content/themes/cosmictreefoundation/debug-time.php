<?php

/**
 * Debug Time Issues
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('../../../wp-blog-header.php');

echo "<h1>Time Debug Information</h1>\n";

$video = VideoBookingDB::get_video(4);
if ($video) {
    echo "<h2>Video Information</h2>\n";
    echo "<p><strong>Title:</strong> {$video->title}</p>\n";
    echo "<p><strong>Early Bird End Date:</strong> {$video->early_bird_end_date}</p>\n";
    echo "<p><strong>Is Early Bird:</strong> " . ($video->is_early_bird ? 'Yes' : 'No') . "</p>\n";

    echo "<h2>Time Comparisons</h2>\n";
    echo "<p><strong>Server Time (date()):</strong> " . date('Y-m-d H:i:s') . "</p>\n";
    echo "<p><strong>WordPress current_time('mysql'):</strong> " . current_time('mysql') . "</p>\n";
    echo "<p><strong>WordPress current_time('timestamp'):</strong> " . current_time('timestamp') . " (" . date('Y-m-d H:i:s', current_time('timestamp')) . ")</p>\n";
    echo "<p><strong>PHP time():</strong> " . time() . " (" . date('Y-m-d H:i:s', time()) . ")</p>\n";

    if ($video->early_bird_end_date) {
        $end_timestamp = strtotime($video->early_bird_end_date);
        $current_timestamp = current_time('timestamp');

        echo "<h2>Early Bird Expiry Check</h2>\n";
        echo "<p><strong>Early Bird End Timestamp:</strong> {$end_timestamp} (" . date('Y-m-d H:i:s', $end_timestamp) . ")</p>\n";
        echo "<p><strong>Current Timestamp:</strong> {$current_timestamp} (" . date('Y-m-d H:i:s', $current_timestamp) . ")</p>\n";
        echo "<p><strong>Is Expired:</strong> " . ($end_timestamp <= $current_timestamp ? 'YES' : 'NO') . "</p>\n";
        echo "<p><strong>Time Difference:</strong> " . ($current_timestamp - $end_timestamp) . " seconds</p>\n";
    }

    echo "<h2>WordPress Settings</h2>\n";
    echo "<p><strong>Timezone String:</strong> " . get_option('timezone_string', 'Not set') . "</p>\n";
    echo "<p><strong>GMT Offset:</strong> " . get_option('gmt_offset', 'Not set') . "</p>\n";
}

echo "<h2>User Access Debug</h2>\n";

// Get user <NAME_EMAIL>
$user = get_user_by('email', '<EMAIL>');
if ($user) {
    echo "<p><strong>User ID:</strong> {$user->ID}</p>\n";

    $user_access = VideoBookingDB::get_user_access($user->ID);
    echo "<p><strong>User Access Records:</strong></p>\n";
    echo "<pre>" . print_r($user_access, true) . "</pre>\n";

    if (!empty($user_access)) {
        foreach ($user_access as $access) {
            echo "<h3>Video: {$access->title}</h3>\n";
            echo "<p><strong>Video ID:</strong> {$access->video_id}</p>\n";
            echo "<p><strong>Is Early Bird:</strong> " . ($access->is_early_bird ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Access Granted At:</strong> {$access->access_granted_at}</p>\n";
            echo "<p><strong>Expires At:</strong> {$access->expires_at}</p>\n";
            echo "<p><strong>Release Date:</strong> {$access->release_date}</p>\n";

            // Test the logic from my_recordings.php
            $current_timestamp = current_time('timestamp');
            $has_access_granted = !empty($access->access_granted_at) && $access->access_granted_at !== '0000-00-00 00:00:00';
            $is_expired = $access->expires_at && $access->expires_at !== '0000-00-00 00:00:00' && strtotime($access->expires_at) < $current_timestamp;

            // For early bird videos, check if release date has passed
            $is_released = true; // Default for regular videos
            $release_date_text = '';

            if ($access->is_early_bird && !empty($access->release_date)) {
                $release_timestamp = strtotime($access->release_date);
                $is_released = $release_timestamp <= $current_timestamp;

                if (!$is_released) {
                    $release_date_text = date('M j, Y g:i A', $release_timestamp);
                }
            }

            // Determine final status
            $is_accessible = $has_access_granted && $is_released && !$is_expired;
            $is_pending = !$has_access_granted || ($access->is_early_bird && !$is_released);

            echo "<h4>Status Logic:</h4>\n";
            echo "<p><strong>Current Timestamp:</strong> {$current_timestamp} (" . date('Y-m-d H:i:s', $current_timestamp) . ")</p>\n";
            echo "<p><strong>Has Access Granted:</strong> " . ($has_access_granted ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Is Expired:</strong> " . ($is_expired ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Is Released:</strong> " . ($is_released ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Release Date Text:</strong> {$release_date_text}</p>\n";
            echo "<p><strong>Is Accessible:</strong> " . ($is_accessible ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Is Pending:</strong> " . ($is_pending ? 'Yes' : 'No') . "</p>\n";

            if ($access->release_date) {
                $release_timestamp = strtotime($access->release_date);
                echo "<p><strong>Release Timestamp:</strong> {$release_timestamp} (" . date('Y-m-d H:i:s', $release_timestamp) . ")</p>\n";
                echo "<p><strong>Time Until Release:</strong> " . ($release_timestamp - $current_timestamp) . " seconds</p>\n";
            }
        }
    }
} else {
    echo "<p>User not found</p>\n";
}

echo "<h2>Manual Release Processing</h2>\n";

// Manually trigger the scheduled release process
$processed_count = VideoBookingAccessControl::process_scheduled_releases();
echo "<p><strong>Processed Videos:</strong> {$processed_count}</p>\n";

// Manually grant early bird access
echo "<h3>Manual Grant Early Bird Access</h3>\n";
$granted_count = VideoBookingAccessControl::grant_early_bird_access(4);
echo "<p><strong>Granted Access Count:</strong> {$granted_count}</p>\n";

// Check the user access again after processing
if ($user) {
    echo "<h3>User Access After Processing:</h3>\n";
    $user_access_after = VideoBookingDB::get_user_access($user->ID);
    echo "<pre>" . print_r($user_access_after, true) . "</pre>\n";
}
